#!/bin/bash

# ===============================================
# AWS SSM Hybrid Activation Creator
# ===============================================
#
# DESCRIPTION:
#   This script creates AWS Systems Manager (SSM) hybrid activation
#   credentials for registering on-premise servers with AWS SSM.
#   These credentials allow servers to authenticate and register
#   with AWS SSM for remote management.
#
# PREREQUISITES:
#   1. AWS CLI installed and configured
#   2. Valid AWS credentials with SSM permissions:
#      - ssm:CreateActivation
#      - ssm:DescribeActivations
#      - iam:PassRole (for SSM service role)
#   3. Existing IAM role: AmazonEC2RunCommandRoleForManagedInstances
#   4. Access to CVLand production account (************)
#
# IMPORTANT - AWS ROLE ASSUMPTION:
#   Before running this script, you MUST assume a role with access to the
#   CVLand production account (************) where SSM activations are created.
#
#   Example role assumption:
#   aws sts assume-role --role-arn arn:aws:iam::************:role/YourSSMRole \
#     --role-session-name ssm-activation-session
#
#   Then export the returned credentials:
#   export AWS_ACCESS_KEY_ID="..."
#   export AWS_SECRET_ACCESS_KEY="..."
#   export AWS_SESSION_TOKEN="..."
#
#   Or set AWS_PROFILE to use a configured profile with the assumed role.
#
# USAGE:
#   1. First assume AWS role for CVLand production account
#   2. Then run: ./scripts/create_ssm_activation.sh [OPTIONS]
#
# OPTIONS:
#   -r, --region REGION         AWS region (default: ap-southeast-2)
#   -a, --account-id ACCOUNT    AWS account ID (default: ************)
#   -i, --iam-role ROLE         IAM role name (default: AmazonEC2RunCommandRoleForManagedInstances)
#   -l, --limit LIMIT           Registration limit (default: 10)
#   -d, --description DESC      Description for the activation
#   -h, --help                  Show help message
#
# EXAMPLES:
#   # Complete workflow with role assumption
#   aws sts assume-role --role-arn arn:aws:iam::************:role/YourSSMRole \
#     --role-session-name ssm-activation
#   export AWS_ACCESS_KEY_ID="..." AWS_SECRET_ACCESS_KEY="..." AWS_SESSION_TOKEN="..."
#   ./scripts/create_ssm_activation.sh
#
#   # Create activation for 50 servers
#   ./scripts/create_ssm_activation.sh --limit 50
#
#   # Create activation with custom description
#   ./scripts/create_ssm_activation.sh --description "Production fleet servers" --limit 25
#
# OUTPUT:
#   - Creates timestamped file: ssm_activation_YYYYMMDD_HHMMSS.txt
#   - Contains activation ID, code, and ready-to-use ansible command
#   - Displays summary and next steps
#
# WHAT IT DOES:
#   1. Validates AWS CLI and credentials
#   2. Checks for required IAM role
#   3. Creates SSM hybrid activation
#   4. Saves credentials to timestamped file
#   5. Provides ansible-playbook command for immediate use
#
# SECURITY NOTES:
#   - Activation credentials are sensitive - treat like passwords
#   - Credentials expire after a set time (default: 30 days)
#   - Each activation has a usage limit (servers that can register)
#   - Store activation files securely and delete when no longer needed
#
# TROUBLESHOOTING:
#   - "Access Denied": Check IAM permissions
#   - "Role not found": Create AmazonEC2RunCommandRoleForManagedInstances role
#   - "Invalid credentials": Run 'aws configure' or set AWS_PROFILE
#
# WORKFLOW:
#   1. Assume AWS role for CVLand production account (************)
#   2. Run this script to create activation credentials
#   3. Use the generated ansible command to register servers
#   4. Verify servers appear in AWS Systems Manager console
#   5. Use Session Manager or Run Command for remote management
#
# ===============================================

set -euo pipefail

# Configuration
AWS_REGION="${AWS_REGION:-ap-southeast-2}"
AWS_ACCOUNT_ID="${AWS_ACCOUNT_ID:-************}"
IAM_ROLE="${IAM_ROLE:-AmazonEC2RunCommandRoleForManagedInstances}"
REGISTRATION_LIMIT="${REGISTRATION_LIMIT:-10}"
DESCRIPTION="${DESCRIPTION:-On-premise servers managed by Ansible}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

usage() {
    cat << EOF
Usage: $0 [OPTIONS]

Create SSM hybrid activation credentials for on-premise servers.

OPTIONS:
    -r, --region REGION         AWS region (default: ap-southeast-2)
    -a, --account-id ACCOUNT    AWS account ID (default: ************)
    -i, --iam-role ROLE         IAM role name (default: AmazonEC2RunCommandRoleForManagedInstances)
    -l, --limit LIMIT           Registration limit (default: 10)
    -d, --description DESC      Description for the activation
    -h, --help                  Show this help message

EXAMPLES:
    # Create activation for default 10 servers
    $0

    # Create activation for 20 servers
    $0 --limit 20

    # Create activation with custom description
    $0 --description "Eyecue fleet servers" --limit 15

ENVIRONMENT VARIABLES:
    AWS_REGION                  Default AWS region
    AWS_ACCOUNT_ID              Default AWS account ID
    AWS_PROFILE                 AWS CLI profile to use

EOF
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -r|--region)
            AWS_REGION="$2"
            shift 2
            ;;
        -a|--account-id)
            AWS_ACCOUNT_ID="$2"
            shift 2
            ;;
        -i|--iam-role)
            IAM_ROLE="$2"
            shift 2
            ;;
        -l|--limit)
            REGISTRATION_LIMIT="$2"
            shift 2
            ;;
        -d|--description)
            DESCRIPTION="$2"
            shift 2
            ;;
        -h|--help)
            usage
            exit 0
            ;;
        *)
            log_error "Unknown option: $1"
            usage
            exit 1
            ;;
    esac
done

# Set default instance name (servers will use their actual hostnames)
DEFAULT_INSTANCE_NAME="fm-tst-nzl-00023"

# Validate requirements
log_info "Validating requirements..."

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    log_error "AWS CLI is not installed. Please install it first."
    exit 1
fi

# Check AWS credentials
if ! aws sts get-caller-identity &> /dev/null; then
    log_error "AWS credentials not configured or invalid."
    log_info "Please run 'aws configure' or set AWS_PROFILE environment variable."
    exit 1
fi

# Verify IAM role exists
log_info "Verifying IAM role exists: $IAM_ROLE"
if ! aws iam get-role --role-name "$IAM_ROLE" --region "$AWS_REGION" &> /dev/null; then
    log_error "IAM role '$IAM_ROLE' does not exist in account $AWS_ACCOUNT_ID"
    log_info "Please create the role or use an existing one."
    exit 1
fi

# Display configuration
log_info "Configuration:"
echo "  AWS Region: $AWS_REGION"
echo "  AWS Account: $AWS_ACCOUNT_ID"
echo "  IAM Role: $IAM_ROLE"
echo "  Registration Limit: $REGISTRATION_LIMIT"
echo "  Default Instance Name: $DEFAULT_INSTANCE_NAME"
echo "  Description: $DESCRIPTION"
echo

# Confirm before proceeding
read -p "Proceed with creating SSM activation? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "Operation cancelled."
    exit 0
fi

# Create SSM activation
log_info "Creating SSM hybrid activation..."

ACTIVATION_RESULT=$(aws ssm create-activation \
    --default-instance-name "$DEFAULT_INSTANCE_NAME" \
    --iam-role "$IAM_ROLE" \
    --registration-limit "$REGISTRATION_LIMIT" \
    --region "$AWS_REGION" \
    --description "$DESCRIPTION" \
    --output json)

if [[ $? -eq 0 ]]; then
    # Parse the result
    ACTIVATION_ID=$(echo "$ACTIVATION_RESULT" | jq -r '.ActivationId')
    ACTIVATION_CODE=$(echo "$ACTIVATION_RESULT" | jq -r '.ActivationCode')
    
    log_success "SSM activation created successfully!"
    echo
    echo "=================================================="
    echo "SSM HYBRID ACTIVATION CREDENTIALS"
    echo "=================================================="
    echo "Activation ID:   $ACTIVATION_ID"
    echo "Activation Code: $ACTIVATION_CODE"
    echo "Region:          $AWS_REGION"
    echo "IAM Role:        $IAM_ROLE"
    echo "Registration Limit: $REGISTRATION_LIMIT"
    echo "=================================================="
    echo
    
    # Generate Ansible command
    log_info "To use these credentials with Ansible, run:"
    echo
    echo "ansible-playbook -i <inventory_file> playbooks/infra_ssm_registration.yml \\"
    echo "  -e \"ssm_activation_id=$ACTIVATION_ID\" \\"
    echo "  -e \"ssm_activation_code=$ACTIVATION_CODE\""
    echo
    echo "Example with specific inventory:"
    echo "ansible-playbook -i tst_hosts.yml playbooks/infra_ssm_registration.yml \\"
    echo "  -e \"ssm_activation_id=$ACTIVATION_ID\" \\"
    echo "  -e \"ssm_activation_code=$ACTIVATION_CODE\""
    echo

    # Save to file
    CREDENTIALS_FILE="ssm_activation_$(date +%Y%m%d_%H%M%S).txt"
    cat > "$CREDENTIALS_FILE" << EOF
# SSM Hybrid Activation Credentials
# Created: $(date)
# Region: $AWS_REGION
# Account: $AWS_ACCOUNT_ID

export SSM_ACTIVATION_ID="$ACTIVATION_ID"
export SSM_ACTIVATION_CODE="$ACTIVATION_CODE"

# Ansible command:
ansible-playbook -i <inventory_file> playbooks/infra_ssm_registration.yml \\
  -e "ssm_activation_id=$ACTIVATION_ID" \\
  -e "ssm_activation_code=$ACTIVATION_CODE"

# Example with specific inventory:
ansible-playbook -i tst_hosts.yml playbooks/infra_ssm_registration.yml \\
  -e "ssm_activation_id=$ACTIVATION_ID" \\
  -e "ssm_activation_code=$ACTIVATION_CODE"
EOF
    
    log_success "Credentials saved to: $CREDENTIALS_FILE"
    log_warning "Keep these credentials secure and delete the file after use!"
    
else
    log_error "Failed to create SSM activation"
    exit 1
fi
