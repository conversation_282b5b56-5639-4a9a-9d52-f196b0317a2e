---

- name: "Cert<PERSON> (for Let's Encrypt)"
  become: yes
  hosts:
  -

  # Note:
  #  We don't need to set "auto renew" because this just creates a cron task,
  #  BUT there's already a systemd timer that runs twice a day for this.
  #  The systemd timer is installed as part of the package and runs at
  #  midnight and midday.
  #
  #  References:
  #    /usr/lib/systemd/system/certbot.timer
  #    /usr/lib/systemd/system/certbot.service
  #    (also /etc/cron.d/certbot)

  vars:
    certbot_admin_email: "<EMAIL>"
    certbot_auto_renew: False
    certbot_create_if_missing: True
    certbot_create_method: "standalone"
    certbot_certs:
    - domains:
      - "test-api.fingermark.dev"

  roles:
  - geerlingguy.certbot


# Web Servers
# -----------
#
# * the web server (e.g. NGINX or Apache) configuration will need to include
#   a reference to the Certbot certificates
#
# NGINX Example
#
# /etc/nginx/sites-enabled/xxxx.conf
#
#   server {
#       listen 443 ssl;
#
#       ssl_certificate /etc/letsencrypt/live/test-api.fingermark.dev/fullchain.pem;
#       ssl_certificate_key /etc/letsencrypt/live/test-api.fingermark.dev/privkey.pem;
#
#       root /usr/share/nginx/html;
#
#       index index.html index.htm index.nginx-debian.html;
#
#       location / {
#           try_files $uri $uri/ =404;
#       }
#   }
