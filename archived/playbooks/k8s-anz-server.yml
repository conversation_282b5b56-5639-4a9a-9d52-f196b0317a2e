---

#
# To run this playbook execute:
# ansible-playbook playbooks/cv-server.yml -v -D -l cv-server
# OPTIONAL: export change_host_name=PUT-YOUR-HOSTNAME-HERE
#

- name: Fingermark Computer Vision server
  hosts: cv-server
  connection: local
  become: yes
  vars:

    # fingermark.base
    default_system_packages:
      - openssh-server
      - python3-pip
      - net-tools

    # fingermark/nvidia-driver
    #TODO: change to latest - Docker image using old version of nvidia docker
    nvidia_driver_version: 390
    nvidia_driver_use_ppa: ppa:graphics-drivers/ppa
    teamviewer_passwd: q1w2e3r4

    # dstil.aws-cli
    aws_region: 'ap-southeast-2'
    aws_access_key_id: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35666565396536663534643638656638376463616437383734653961346432656165366265363436
          3838363437323031613632646166623935363835356361360a373261396465663635356539373733
          66383434393633396635643333373137376135356665303338353664643664366537376139353064
          3963373830366532340a663766663362336134393937663832383538643930643933653065346238
          36633362396138653334373433323864653064396233636239386636363631613163
    aws_secret_access_key: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          35336265633039663738666330633135363131633736623932663231343765323837343439353430
          3430333863633539333234323536376561323864323338390a666364633830383335393930363339
          65303435363865373938646133376234346562343531316465323033303333366539393431306631
          6135316137333131660a323335356137306265656634363839656130666632386164396263333430
          34666162353262353536666439323361636237646332663639363263313635373830366136653735
          6531333461666435303362643462653331663739646235616538

    # fingermark.setup-1tb-volume
    storage_device_label: storage
    storage_mount_path: /media/fingermark/storage

    # fingermark.set-up-dirs
    set_up_dirs:
      - /opt/safebelt/
      - /opt/safebelt/detector_files
      - /opt/safebelt/run_files
    set_up_dirs_owner: "fingermark"
    set_up_dirs_group: "fingermark"
    set_up_dirs_permission: 0755

     # fingermark.network-interface
    secondary_iface_address: "***********"
    secondary_iface_netmask: "*************"

    # fingermark.belt-safety-gateway
    belt_safety_gateway_config_file_path: /usr/share/fingermark/belt-safety/settings/settings.json
  
  # tasks: 
  #   - name: Change hostname
  #     hostname:
  #        name: "{{ change_host_name }}"
  #      when: change_host_name is defined
  roles:
    # Not sure why need swap
    - {role: 'fingermark.swap', tags: 'fingermark.swap'} #added for safebelt
    - {role: 'fingermark.base', tags: 'fingermark.base'} #added for safebelt
    - {role: 'cmprescott.chrome', tags: 'cmprescott.chrome'}
    - {role: 'fingermark.nvidia-driver', tags: 'fingermark.nvidia-driver'}
    - {role: 'geerlingguy.docker', tags: 'geerlingguy.docker'}
    - {role: 'fingermark.nvidia-docker', tags: 'fingermark.nvidia-docker'}
    - {role: 'wtanaka.teamviewer', tags: 'wtanaka.teamviewer'}
    - {role: 'fingermark.teamviewer-postinstall', tags: 'fingermark.teamviewer-postinstall'}
    # added for safbelt 

    #Won't work with the lenovo units - using nvme
    # - {role: 'fingermark.setup-1tb-volume', tags: 'fingermark.setup-1tb-volume'}
    # - {role: 'fingermark.set-up-dirs', tags: 'fingermark.set-up-dirs'}
    - {role: 'dstil.aws-cli', become_user: 'root' , tags: 'dstil.aws-cli-root'}
    - {role: 'dstil.aws-cli', become_user: 'fingermark' , tags: 'dstil.aws-cli'}
    - {role: 'fingermark.network-interface-dynamic', tags: 'fingermark.network-interface-dynamic'}
    - {role: 'fingermark.belt-safety-gateway', tags: 'fingermark.belt-safety-gateway'}


- hosts: master
  connection: local
  gather_facts: yes
  become: yes
  roles:
    - { role: kubernetes/master, tags: master }
    - { role: cni, tags: cni }
    # - { role: 'fingermark.updater', tags: 'fingermark.updater' }
