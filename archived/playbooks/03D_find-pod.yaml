---
- name: Find pod
  become: true
  gather_facts: false
  hosts:
    - all
  tasks:
    - name: Check for the desired pod
      command: kubectl --kubeconfig /etc/kubernetes/admin.conf get pods -A
      register: kubectl_output
      changed_when: false

    - name: copy the output to a local file
      become: false
      copy:
        content: "{{ kubectl_output.stdout }}"
        dest: "/tmp/{{ inventory_hostname }}.txt"
      delegate_to: localhost
