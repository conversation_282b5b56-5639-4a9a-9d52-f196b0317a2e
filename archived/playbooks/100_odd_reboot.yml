---
- name: "Out-of-schedule Reboot"
  gather_facts: no
  become: yes
  hosts:
  - hastings_test

  vars:
  - schedule: "0 3 * * 5"

  tasks:
  - name: "Creating cron file"
    file:
      path: "/tmp/reboot"
      state: touch

  - name: "Adding cron line"
    lineinfile:
      line: "{{ schedule }} root /sbin/shutdown -r +5"
      path: "/tmp/reboot"
      regexp: "shutdown -r"

  - name: "Creating symbolic link"
    file: 
      src: "/tmp/reboot"
      dest: "/etc/cron.d/reboot"
      state: link