---

- name: "Reset Kubernetes cluster using kubeadm reset"
  hosts: "{{ run_hosts | split(',') | default('localhost')}}"
  gather_facts: true
  become: true 
  vars:
    delete_media_files: false
    microk8s: true
    reset_docker: false

  vars_prompt:
    - name: continue
      prompt: You are about to reset docker and Kubernetes cluster. Do you want to continue? (y/N)
      private: false

  tasks:
    - fail:
      when: "continue != 'y'"

    - name: Remove /media content
      file: 
        path: /media/fingermark/storage/*
        state: absent  
      when: delete_media_files
      
    - name: Reset Kubernetes component
      shell: "kubeadm reset --force"
      ignore_errors: True

    - name: Delete etcd content
      file:
        path: /var/lib/etcd/
        state: absent

    - name: Delete /etc/kubernetes content
      file:
        path: /etc/kubernetes/
        state: absent

    - name: Deleting /etc/cni/net.d content
      file:
        path: /etc/cni/net.d/
        state: absent

    - name: Delete /etc/kubernetes/kubelet.conf
      file:
        path: /etc/kubernetes/kubelet.conf
        state: absent

    - name: Delete /etc/kubernetes/pki/ca.crt
      file:
        path: /etc/kubernetes/pki/ca.crt
        state: absent
    

    - name: "Removing old binary files"
      ansible.builtin.apt:
        name:
          - kubeadm
          - kubelet
          - kubectl
        state: absent
      when: microk8s
      ignore_errors: true

    - name: Stop docker daemon
      systemd:
        name: docker
        state: stopped
      tags: 
        - docker
      ignore_errors: true
      when: reset_docker|bool == true

    - name: Reset docker environment
      shell: "docker system prune -a"
      ignore_errors: True
      tags: 
        - docker
      when: reset_docker|bool == true
      
    - name: "Removing docker packages"
      ansible.builtin.apt:
        name:
          - docker-ce  
          - docker-ce-cli
          - docker-ce-rootless-extras
        state: absent
        purge: true
        autoremove: true
      tags: 
        - docker
      when: reset_docker|bool == true
  
    - name: Reboot host and wait for it to restart
      reboot:
        msg: "Reboot initiated by Ansible"
        connect_timeout: 5
        reboot_timeout: 600
        pre_reboot_delay: 0
        post_reboot_delay: 30
        test_command: whoami
      tags: 
        - docker
      when: reset_docker|bool == true

    - name: Delete docker container files
      file:
        path: /var/lib/docker
        state: absent
      tags: 
        - docker
      when: reset_docker|bool == true        
    
    - name: Delete docker config
      file:
        path: /etc/docker
        state: absent
      tags: 
        - docker
      when: reset_docker|bool == true

    - name: Delete docker stuff
      file:
        path: /etc/apparmor.d/docker
        state: absent
      tags: 
        - docker
      when: reset_docker|bool == true

    - name: Delete docker stuff
      file:
        path: /var/run/docker.sock
        state: absent
      tags: 
        - docker
      when: reset_docker|bool == true

    - name: Reinstall Docker
      include_role:
        name: geerlingguy.docker 
      vars:
        docker_version: "5:19.03.10~3-0~ubuntu-{{ ansible_distribution_release }}"
      tags:
        - docker
      when: reset_docker|bool == true

    - name: Setup nvidia-docker again 
      include_role:
        name: fingermark.nvidia-docker
      tags:
        - nvidia
        - nvidia-docker
      when: reset_docker|bool == true
