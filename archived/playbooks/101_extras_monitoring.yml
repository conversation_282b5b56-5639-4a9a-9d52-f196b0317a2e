---
- name: "Extra tasks for the monitoring"
  become: yes
  gather_facts: no
  hosts:
  - murdoch
  vars:
  - thanos_version: "v0.17.2"

  tasks:
  # - name: Chaning Thanos version
  #   shell: "kubectl --kubeconfig /etc/kubernetes/admin.conf patch -n monitoring prometheus monitor-kube-prometheus-st-prometheus --type='json' -p='[{ \"op\": \"replace\", \"path\": \"/spec/thanos/version\", \"value\":\"{{ thanos_version }}\"}]'"


  # - name: Delete monitoring
  #   shell: helm --kubeconfig /etc/kubernetes/admin.conf uninstall monitor -n monitoring

  - shell: |
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd alertmanagerconfigs.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd alertmanagers.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd podmonitors.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd probes.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd prometheuses.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd prometheusrules.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd servicemonitors.monitoring.coreos.com
      kubectl --kubeconfig /etc/kubernetes/admin.conf delete crd thanosrulers.monitoring.coreos.com

  - shell: kubectl --kubeconfig /etc/kubernetes/admin.conf delete ns monitoring