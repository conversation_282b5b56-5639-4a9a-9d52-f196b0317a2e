FROM ubuntu:20.04

ENV DEBIAN_FRONTEND=noninteractive

ARG DEV_USERNAME=developer
ARG DEV_HOME_DIR=/home/<USER>
ARG DEV_USER_ID=1000
ARG DEV_GROUP_ID=1000

RUN       addgroup --gid "${DEV_GROUP_ID}" "${DEV_USERNAME}"\
    &&    adduser "${DEV_USERNAME}" \
          --uid "${DEV_USER_ID}" \
          --gid "${DEV_GROUP_ID}" \
          --gecos "" \
          --disabled-login \
          --disabled-password \
          --home "${DEV_HOME_DIR}"

RUN     apt-get update \
    &&  apt-get install -y \
            nano \
            apt-transport-https \
            ca-certificates \
            apt-utils \
            curl \
            openssh-client \
            iputils-ping \
            gnupg \
            sshpass \
            python3 \
            python3-pip \
            python-is-python3 \
            jq \
            rsync \
            git \
            make \
    &&  mkdir -p /opt/project

WORKDIR /opt/project

# Install Pip requirements
COPY requirements.txt .
RUN pip3 install -r requirements.txt

# Install ansible additions
#RUN     ansible-galaxy collection install community.general

USER ${DEV_USERNAME}

RUN     mkdir -p ~/.ssh \
    &&  chmod 700 ~/.ssh
