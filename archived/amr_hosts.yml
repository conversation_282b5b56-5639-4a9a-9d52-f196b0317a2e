all:
  vars:
    ansible_python_interpreter: /usr/bin/python3
    fingermark_product: eyecue
    customer: amr
    kubeadmin_config: "/etc/kubernetes/admin.conf"    
  children:
    infrastructure:
      children:
        icinga2_satellite:
          hosts: 
            icinga2-satellite.amr.infra.fingermark.tech
          vars:
            softether_client_private_ip: **********
            icinga2_satellite_endpoint: api.icinga2-master.infra.fingermark.tech
    production:
      vars:        
        pod_network_cidr: "**********/16"        
        icinga2_satellite_endpoint: "icinga2-satellite.amr.infra.fingermark.tech"
        meshid: "XbnehF9XD7XYjxHy"
        argocd_bitbucket_repo: "*****************:fingermarkltd/eyecue-amr-helm"
      children:                
        # dubai_server: (MOVED TO POC)
        #   hosts:
        #       fm-amr-uae-001.eyeq.vpn
        #   vars:
        #     coordinates: "25.254059,55.302948"
        #     display_name: "Dubai"
        #     timezone:
