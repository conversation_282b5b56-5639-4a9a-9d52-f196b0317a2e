---
- name: Reboot EyeCue server
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"
  become: true

  vars:
    run_host: "localhost"
    reboot_delay: 10

  tasks:
    - name: Display reboot initiation message
      ansible.builtin.debug:
        msg: Initiating reboot of {{ inventory_hostname }} in {{ reboot_delay }} seconds.

    - name: Reboot {{ inventory_hostname }}
      ansible.builtin.shell: |
        nohup bash -c "sleep {{ reboot_delay }} && shutdown -r now 'Rebooting via AWX'" >/dev/null 2>&1 &
      changed_when: false
