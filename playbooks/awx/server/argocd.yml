---
- # NOTE: We set the tag to 'argocd' in AWX so that only the argocd role is run
  name: Setup ArgoCD
  ansible.builtin.import_playbook: ../../eyecue_full_setup.yml
  vars:
    argocd_force_sync: true
    run_hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"
    run_host: "localhost"
  pre_tasks:
    - name: Set argocd_helm_bucket_name based on ansible_host
      set_fact:
        argocd_helm_bucket_name: "{{ 'eyecue-helm-cv-prod-us-package' if 'cfa-usa' in ansible_host else 'eyecue-helm-cv-prod-package' }}"
