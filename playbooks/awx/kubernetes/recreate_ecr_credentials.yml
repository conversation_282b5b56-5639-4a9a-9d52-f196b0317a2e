---
- name: Create Kubernetes ECR Job from CronJob
  become: true
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"

  vars:
    run_host: "localhost"
    cronjob_name: "ecr-cred-updater"
    new_cronjob_name: "ecr-cred-updater-awx-{{ '%05d' | format(99999 | random) }}"
    kubeconfig_file: "/etc/kubernetes/admin.conf"
    namespaces:
      - infra
      - nmp-{{ ansible_hostname }}
      - monitoring

  tasks:
    - name: Create Kubernetes Job from CronJob
      ansible.builtin.command:
        kubectl create job --from=cronjob/{{ cronjob_name }} {{ new_cronjob_name }} -n {{ namespace }}
      environment:
        KUBECONFIG: "{{ kubeconfig_file }}"
      register: job_output
      loop: "{{ namespaces }}"
      loop_control:
        loop_var: namespace

    - name: Display job creation results
      ansible.builtin.debug:
        msg: |
          Job Creation Summary:

          {% for result in job_output.results -%}
          - Namespace: {{ result.namespace }}
            Status: {{ 'SUCCESS' if result.rc == 0 else 'FAILED' }}
            Job Name: {{ result.stdout.split('/')[-1].split(' ')[0] if result.rc == 0 else 'N/A' }}
            {%- if result.rc != 0 %}
            Error: {{ result.stderr }}
            {%- endif %}

          {% endfor -%}
