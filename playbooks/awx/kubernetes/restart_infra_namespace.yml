---
- name: Restart Infra namespace in Kubernetes
  become: true
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"

  vars:
    run_host: "fm-dev-nzl-00001"
    kubeconfig_file: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Restart Infra namespace
      kubernetes.core.k8s:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Pod
        namespace: infra
        delete_all: true
        state: absent

    - ansible.builtin.debug:
        msg: "Infra namespace has been restarted."
