---
- name: Restart CoreDNS pods in Kubernetes
  become: true
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"

  vars:
    run_host: "localhost"
    kubeconfig_file: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Rollout restart CoreDNS deployment
      kubernetes.core.k8s:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Deployment
        name: coredns
        namespace: kube-system
        definition:
          spec:
            template:
              metadata:
                annotations:
                  kubectl.kubernetes.io/restartedAt: "{{ ansible_date_time.iso8601 }}"

    - name: Wait for CoreDNS rollout to complete
      kubernetes.core.k8s_info:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Deployment
        name: coredns
        namespace: kube-system
        wait: true
        wait_condition:
          type: Progressing
          status: "True"
          reason: NewReplicaSetAvailable
        wait_timeout: 300

    - name: "CoreDNS deployment restarted successfully"
      ansible.builtin.debug:
        msg: "CoreDNS deployment has been restarted."
