---
- name: Restart Eyecue namespace in Kubernetes
  become: true
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"

  vars:
    run_host: "localhost"
    kubeconfig_file: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Restart ArgoCD namespace
      kubernetes.core.k8s:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Pod
        namespace: nmp-{{ ansible_hostname }}
        delete_all: true
        state: absent

    - ansible.builtin.debug:
        msg: "Eyecue namespace has been restarted."
