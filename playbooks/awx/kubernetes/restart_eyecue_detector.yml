---
- name: Restart eyeq-detector in Kubernetes
  become: true
  hosts: "{{ run_host if run_host.endswith('.eyeq.vpn') else run_host ~ '.eyeq.vpn' }}"

  vars:
    run_host: "localhost"
    kubeconfig_file: "/etc/kubernetes/admin.conf"

  tasks:
    - name: Restart eyeq-detector
      kubernetes.core.k8s:
        kubeconfig: "{{ kubeconfig_file }}"
        kind: Pod
        namespace: nmp-{{ ansible_hostname }}
        label_selectors: app=eyeq-detector
        state: absent

    - ansible.builtin.debug:
        msg: "eyeq-detector pods have been restarted successfully."
