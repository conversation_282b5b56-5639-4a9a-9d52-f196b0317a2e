---
- name: Securely wipe target servers and power off
  hosts: "{{ run_hosts | split(',') }}"
  become: true
  gather_facts: true
  gather_subset:
    - hardware

  ############################
  # ---- Runtime Vars ----  #
  ############################
  vars:
    shred_passes: 3
    tmpfs_path: /dev/shm
    script_name: wipe.sh

  ############################
  # ---- Safeguard Gate ---- #
  ############################
  pre_tasks:
    - name: Abort unless operator explicitly confirms
      fail:
        msg: >
          Playbook execution cancelled — you must pass
          --extra-vars "confirm_wipe=true" for this to run.
      when: confirm_wipe is not defined or not confirm_wipe

  ################################
  # ---- Determining Drives ---- #
  ################################
    - name: Gather list of drives to wipe
      shell: | # Root device must be last, so we can wipe it last.
        lsblk -dno NAME,TYPE,RM | awk '$2 == "disk" && $3 == "0" {print "/dev/" $1}'
      register: drives_to_wipe
      changed_when: false

    - name: Set wipe drives variable
      set_fact:
        wipe_drives: "{{ drives_to_wipe.stdout_lines }}"

    - name: Output drives to be wiped
      debug:
        msg: "Drives to be wiped: {{ wipe_drives }}"

  tasks:
    #################################################################
    # Make sure /dev/shm is mounted exec-capable (some distros    #
    # set noexec; we remount it rw,exec so the RAM-resident tools #
    # can run).                                                   #
    #################################################################
    - name: Ensure tmpfs mount is present and exec-capable
      mount:
        path: "{{ tmpfs_path }}"
        src: tmpfs
        fstype: tmpfs
        opts: "rw,nosuid,nodev,mode=0700"
        state: mounted

    #################################################################
    # Create a private /proc inside /dev/shm.  This keeps access  #
    # to /proc/sysrq-trigger after the root FS is destroyed.      #
    #################################################################
    - name: Create proc mount point inside tmpfs
      file:
        path: "{{ tmpfs_path }}/proc"
        state: directory
        mode: "0700"

    - name: Mount a fresh /proc in tmpfs
      command: mount -t proc proc "{{ tmpfs_path }}/proc"
      args:
        creates: "{{ tmpfs_path }}/proc/cmdline"

    #################################################################
    # Copy the shred binary into RAM (plus its libs automatically #
    # pulled in via copy module’s remote_src).                    #
    #################################################################
    - name: Copy shred binary to RAM
      copy:
        remote_src: true
        src: /usr/bin/shred
        dest: "{{ tmpfs_path }}/shred"
        mode: "0755"

    #################################################################
    # Build the self-destruct script entirely in RAM.             #
    #################################################################
    - name: Build wipe script in RAM
      copy:
        dest: "{{ tmpfs_path }}/{{ script_name }}"
        mode: "0700"
        content: |
          #!/usr/bin/env bash
          set -euo pipefail

          #############################################
          # Unmount likely data mounts (best-effort). #
          #############################################
          umount /home || true
          umount -R /mnt || true
          umount -R /media || true
          umount -R /media/fingermark/storage || true

          #############################################
          # Wipe secondary drives first               #
          #############################################
          {% for drive in wipe_drives[:-1] %}
                    "{{ tmpfs_path }}/shred" -v -n {{ shred_passes }} -z {{ drive }}
          {% endfor %}

          #############################################
          # Finally wipe the root drive               #
          #############################################
          "{{ tmpfs_path }}/shred" -v -n {{ shred_passes }} -z {{ wipe_drives[-1] }}

          #############################################
          # Power off via SysRq (clears RAM)          #
          #############################################
          echo 1 > "{{ tmpfs_path }}/proc/sys/kernel/sysrq"
          echo o > "{{ tmpfs_path }}/proc/sysrq-trigger"

    #################################################################
    # Launch the wipe script asynchronously — we do not wait.    #
    #################################################################
    - name: Launch irreversible wipe (fire-and-forget)
      shell: "nohup {{ tmpfs_path }}/{{ script_name }} > {{ tmpfs_path }}/wipe.log 2>&1 &"
      async: 0
      poll: 0
      ignore_errors: true

    - name: Inform user of wipe initiation
      debug:
        msg: |
          ⚠️ [WARNING] Initiated self destruction.
            The server will overwrite the drives {{ shred_passes }}x with random data - this can take a while!
            The server will power off after wiping all drives.
            
            Check with `watch cat {{ tmpfs_path }}/wipe.log` for details."
