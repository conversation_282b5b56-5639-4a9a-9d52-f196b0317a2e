---
# Playbook to check kernel status and identify systems needing the fix

- name: Check kernel status on Ubuntu 22.04 systems
  hosts: "{{ run_hosts | default('all') }}"
  gather_facts: yes
  become: yes

  tasks:
    - name: Gather system information
      setup:
        gather_subset:
          - distribution
          - kernel

    - name: Check if system is Ubuntu 22.04
      set_fact:
        is_ubuntu_2204: "{{ ansible_distribution == 'Ubuntu' and ansible_distribution_version == '22.04' }}"

    - name: Get current kernel version
      command: uname -r
      register: current_kernel
      changed_when: false
      when: is_ubuntu_2204

    - name: Check if Lacework datacollector is installed
      stat:
        path: /var/lib/lacework
      register: lacework_installed
      when: is_ubuntu_2204

    - name: Check Lacework datacollector service status
      systemd:
        name: datacollector
      register: lacework_service
      failed_when: false
      changed_when: false
      when: 
        - is_ubuntu_2204
        - lacework_installed.stat.exists | default(false)

    - name: Check for held kernel packages
      shell: dpkg --get-selections | grep -E "linux-(image|headers|modules)" | grep hold || true
      register: held_packages
      changed_when: false
      when: is_ubuntu_2204

    - name: Check for problematic kernel versions
      set_fact:
        has_problematic_kernel: "{{ current_kernel.stdout is search('5.15.0-14[4-9]') or current_kernel.stdout is search('5.15.0-15[0-9]') }}"
      when: is_ubuntu_2204

    - name: Display system status
      debug:
        msg: |
          Hostname: {{ inventory_hostname }}
          OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
          Current Kernel: {{ current_kernel.stdout | default('N/A') }}
          Problematic Kernel: {{ has_problematic_kernel | default(false) }}
          Lacework Installed: {{ lacework_installed.stat.exists | default(false) }}
          Lacework Service: {{ lacework_service.status.ActiveState | default('not installed') }}
          Held Packages: {{ held_packages.stdout_lines | default([]) | length }} packages
          Action Required: {{ (is_ubuntu_2204 and has_problematic_kernel | default(false)) | ternary('YES - Apply kernel fix', 'No action needed') }}
      when: is_ubuntu_2204

    - name: Summary for non-Ubuntu 22.04 systems
      debug:
        msg: |
          Hostname: {{ inventory_hostname }}
          OS: {{ ansible_distribution }} {{ ansible_distribution_version }}
          Status: Not affected (not Ubuntu 22.04)
      when: not is_ubuntu_2204

    - name: Create report of affected systems
      set_fact:
        affected_system:
          hostname: "{{ inventory_hostname }}"
          kernel: "{{ current_kernel.stdout | default('N/A') }}"
          lacework: "{{ lacework_installed.stat.exists | default(false) }}"
          action_required: true
      when:
        - is_ubuntu_2204
        - has_problematic_kernel | default(false)

    - name: Aggregate affected systems
      set_fact:
        all_affected_systems: "{{ groups['all'] | map('extract', hostvars, 'affected_system') | select('defined') | list }}"
      run_once: true

    - name: Display summary report
      debug:
        msg: |
          ========================================
          KERNEL FIX ASSESSMENT SUMMARY
          ========================================
          Total hosts checked: {{ play_hosts | length }}
          Ubuntu 22.04 hosts: {{ play_hosts | map('extract', hostvars, 'is_ubuntu_2204') | select | list | length }}
          Hosts needing fix: {{ all_affected_systems | default([]) | length }}
          
          Affected hosts:
          {% for system in all_affected_systems | default([]) %}
          - {{ system.hostname }} (kernel: {{ system.kernel }}, lacework: {{ system.lacework }})
          {% endfor %}
          ========================================
      run_once: true
      when: all_affected_systems is defined