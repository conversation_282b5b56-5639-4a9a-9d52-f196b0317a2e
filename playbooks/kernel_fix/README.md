# Kernel Fix - Ubuntu 22.04

## Overview

Fix for Lacework Datacollector kernel soft lockup issue by installing and pinning kernel 5.15.0-143-generic.

**Total Servers:** 508  
**Total Batches:** 59 (10 hosts per batch)  
**Processing:** Serial (1 host at a time)  
**Time per Host:** ~5-10 minutes

## Files

| File | Description |
|------|-------------|
| `kernel_fix.yml` | Main playbook for kernel fix |
| `inventory.ini` | Hosts organized in batches |
| `check_kernel_status.yml` | Validation playbook |

## Quick Commands

### Execute Single Batch

```bash
ansible-playbook -i inventory.ini kernel_fix.yml --limit batch_001
```

### Execute Range of Batches

```bash
for i in {001..010}; do
  ansible-playbook -i inventory.ini kernel_fix.yml --limit batch_$i
  sleep 30
done
```

### Execute by Customer

```bash
# All McDonald's Australia hosts (batches 016-052)
ansible-playbook -i inventory.ini kernel_fix.yml --limit fm-mcd-aus

# All Burger King NZ hosts (batches 001-006)
ansible-playbook -i inventory.ini kernel_fix.yml --limit fm-bkg-nzl
```

### Check Status

```bash
# Check kernel version for a batch
ansible -i inventory.ini batch_001 -m shell -a "uname -r"

# Run validation playbook
ansible-playbook -i inventory.ini check_kernel_status.yml --limit batch_001
```

## Batch Mapping

### BKG - Burger King

| Batches | Location | Hosts |
|---------|----------|-------|
| 001-006 | New Zealand | 57 |
| 007 | USA | 2 |

### MCD - McDonald's

| Batches | Location | Hosts |
|---------|----------|-------|
| 016-052 | Australia | 370 |
| 053 | Canada | 1 |

### Other Customers

| Batches | Customer | Location | Hosts |
|---------|----------|----------|-------|
| 008 | CFA | USA | 6 |
| 009 | CUL | USA | 1 |
| 010-012 | CZP | USA | 24 |
| 013-014 | ELJ | Australia | 16 |
| 015 | KFC | Australia | 3 |
| 054-055 | MNZ | New Zealand | 12 |
| 056 | POP | New Zealand | 8 |
| 057 | STB | New Zealand | 5 |
| 058 | TIM | Canada | 2 |
| 059 | ZMB | Australia | 1 |

## Execution Examples

### Sequential Execution (Recommended)

```bash
# Process batches 1-5
for batch in *********** 004 005; do
  echo "Processing batch_$batch..."
  ansible-playbook -i inventory.ini kernel_fix.yml --limit batch_$batch
  
  # Wait 30 seconds between batches
  sleep 30
done
```

### Parallel Check (Status Only)

```bash
# Check multiple batches at once (read-only)
ansible -i inventory.ini batch_001:batch_002:batch_003 -m shell -a "uname -r"
```

### Progress Tracking

```bash
# Check which hosts need the fix
ansible-playbook -i inventory.ini check_kernel_status.yml

# After running some batches, check again
ansible-playbook -i inventory.ini check_kernel_status.yml --limit fm-mcd-aus
```

## Important Notes

1. **Package Hold**: Even if already on kernel 5.15.0-143, the playbook ensures packages are held
2. **Smart Skip**: Only skips hosts that have both correct kernel AND held packages
3. **Reboot**: Only reboots if kernel installation is needed
4. **Serial Processing**: Only 1 host is processed at a time within each batch for safety
5. **Retry Safe**: You can safely re-run any batch; fully fixed hosts will be skipped

## Troubleshooting

### Connection Timeout

Server may be rebooting. Wait 5 minutes and retry.

### APT Lock Error

Another process is using apt. Wait and retry.

### Verification

After completion, verify with:

```bash
ansible -i inventory.ini <batch_or_group> -m shell -a "dpkg -l | grep linux-image | grep ^ii"
```

## Complete All Batches

To process all 59 batches:

```bash
#!/bin/bash
for i in {001..059}; do
  echo "========================================="
  echo "Processing batch_$i ($(date))"
  echo "========================================="
  
  ansible-playbook -i inventory.ini kernel_fix.yml --limit batch_$i
  
  if [ $? -ne 0 ]; then
    echo "Error in batch_$i. Stopping."
    exit 1
  fi
  
  # Skip wait for last batch
  if [ "$i" != "059" ]; then
    echo "Waiting 30 seconds..."
    sleep 30
  fi
done

echo "All batches completed!"
```
