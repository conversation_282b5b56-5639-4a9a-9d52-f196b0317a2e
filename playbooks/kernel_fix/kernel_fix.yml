---
# Simple Kernel Fix Playbook - The Ansible Way
# Usage: ansible-playbook -i kernel_fix_inventory.ini kernel_fix_simple.yml --limit batch_01

- name: Apply Kernel Fix to Ubuntu 22.04 Systems
  hosts: 
    - fm-bkg-nzl-00058.eyeq.vpn
  gather_facts: yes
  become: yes
  serial: 1   # Process one at a time (or use 10 for batches)
  
  vars:
    target_kernel: "5.15.0-143"
    ansible_host_key_checking: false
    
  tasks:
    - name: Check if Ubuntu 22.04
      assert:
        that:
          - ansible_distribution == "Ubuntu"
          - ansible_distribution_version == "22.04"
        fail_msg: "Skipping non-Ubuntu 22.04 system"
      
    - name: Check current kernel
      command: uname -r
      register: current_kernel
      changed_when: false
      
    - name: Check if kernel packages are held
      shell: dpkg --get-selections | grep -E "linux-(image|headers|modules|modules-extra)-{{ target_kernel }}-generic" | grep hold | wc -l
      register: held_count
      changed_when: false
      
    - name: Set skip flag
      set_fact:
        should_skip: "{{ (target_kernel in current_kernel.stdout) and (held_count.stdout | int >= 4) }}"
      
    - name: Skip if already fixed (kernel OK and packages held)
      meta: end_host
      when: should_skip | bool
      
    - name: Install kernel packages (if not already on target)
      apt:
        name:
          - "linux-image-{{ target_kernel }}-generic"
          - "linux-headers-{{ target_kernel }}-generic"
          - "linux-modules-{{ target_kernel }}-generic"
          - "linux-modules-extra-{{ target_kernel }}-generic"
        state: present
        update_cache: yes
        allow_downgrade: yes
      when: target_kernel not in current_kernel.stdout
        
    - name: Hold kernel packages
      dpkg_selections:
        name: "{{ item }}"
        selection: hold
      loop:
        - "linux-image-{{ target_kernel }}-generic"
        - "linux-headers-{{ target_kernel }}-generic"
        - "linux-modules-{{ target_kernel }}-generic"
        - "linux-modules-extra-{{ target_kernel }}-generic"

    - name: Check if Lacework datacollector service exists
      systemd:
        name: datacollector
      register: lacework_service
      failed_when: false
      changed_when: false
      
    - name: Stop and disable Lacework datacollector service
      systemd:
        name: datacollector
        state: stopped
        enabled: no
        masked: yes
      when:
        - lacework_service.status is defined
        - lacework_service.status.ActiveState is defined
      register: lacework_disabled
      
    - name: Display Lacework status
      debug:
        msg: "Lacework datacollector has been disabled and masked. It will be deployed via Helm in the future."
      when: lacework_disabled.changed | default(false)

    - name: Update GRUB
      command: update-grub
      when: target_kernel not in current_kernel.stdout

    - name: Find target kernel menuentry in GRUB
      shell: |
        grep menuentry /boot/grub/grub.cfg | grep "{{ target_kernel }}-generic" | grep -v "recovery mode" | head -1
      register: target_menuentry
      changed_when: false
      when: target_kernel not in current_kernel.stdout

    - name: Extract menuentry ID from GRUB config
      shell: |
        echo "{{ target_menuentry.stdout }}" | sed -n "s/.*\$menuentry_id_option '\([^']*\)'.*/\1/p"
      register: menuentry_id
      changed_when: false
      when:
        - target_kernel not in current_kernel.stdout
        - target_menuentry.stdout != ""

    - name: Set GRUB default to target kernel using menuentry ID
      lineinfile:
        path: /etc/default/grub
        regexp: '^GRUB_DEFAULT='
        line: 'GRUB_DEFAULT="{{ menuentry_id.stdout }}"'
        backup: yes
      when:
        - target_kernel not in current_kernel.stdout
        - menuentry_id.stdout != ""
      register: grub_config_updated

    - name: Update GRUB configuration
      command: update-grub
      when: grub_config_updated.changed | default(false)

    - name: Display GRUB configuration info
      debug:
        msg: |
          GRUB has been configured to boot {{ target_kernel }}-generic kernel.
          Menuentry ID: {{ menuentry_id.stdout }}
          GRUB_DEFAULT set to: {{ menuentry_id.stdout }}
          System will reboot to apply changes.
      when:
        - target_kernel not in current_kernel.stdout
        - menuentry_id.stdout != ""

    - name: Reboot system to apply kernel changes
      reboot:
        reboot_timeout: 300
        connect_timeout: 20
        test_command: uptime
      when:
        - target_kernel not in current_kernel.stdout
        - menuentry_id.stdout != ""

    - name: Verify kernel after reboot
      command: uname -r
      register: new_kernel
      changed_when: false
      when: target_kernel not in current_kernel.stdout

    - name: Display kernel verification result
      debug:
        msg: |
          Kernel fix verification:
          Expected: {{ target_kernel }}-generic
          Current: {{ new_kernel.stdout }}
          Status: {{ 'SUCCESS' if target_kernel in new_kernel.stdout else 'FAILED - Manual intervention required' }}
      when:
        - target_kernel not in current_kernel.stdout
        - new_kernel.stdout is defined
      
    # - name: Display status for hosts already on target kernel
    #   debug:
    #     msg: "Host already on kernel {{ target_kernel }}, only applied package hold"
    #   when: target_kernel in current_kernel.stdout
      
