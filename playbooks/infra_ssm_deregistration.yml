# ===============================================
# AWS Systems Manager (SSM) Cleanup Playbook
# ===============================================
#
# DESCRIPTION:
#   This playbook completely removes SSM agent and all related configurations
#   from on-premise servers. Use this to clean up before fresh SSM registration
#   or when decommissioning servers from AWS SSM management.
#
# USAGE:
#   ansible-playbook -i <inventory_file> playbooks/infra_ssm_cleanup.yml
#
# EXAMPLE:
#   ansible-playbook -i tst_hosts.yml playbooks/infra_ssm_cleanup.yml
#
# WHAT IT DOES:
#   - Stops and disables SSM Agent service
#   - Removes SSM Agent package/snap
#   - Deletes all SSM configuration files and directories
#   - Removes SSM logs and cache
#   - Cleans up AWS CLI if installed by SSM playbook
#   - Verifies complete removal
#
# WARNING:
#   This will completely remove the server from AWS SSM management.
#   The server will no longer be accessible via Session Manager or Run Command.
#
# ===============================================

---
- hosts: "{{ run_hosts.split(',') }}"

  become: true
  tasks:
    - name: Display cleanup information
      debug:
        msg: |
          🧹 SSM Agent Cleanup Starting
          Hostname: {{ inventory_hostname }}
          This will completely remove SSM agent and configurations

    # ===============================================
    # Stop and Disable SSM Agent
    # ===============================================
    - name: Stop SSM Agent service (snap version)
      systemd:
        name: snap.amazon-ssm-agent.amazon-ssm-agent
        state: stopped
        enabled: no
      ignore_errors: yes

    - name: Stop SSM Agent service (package version)
      systemd:
        name: amazon-ssm-agent
        state: stopped
        enabled: no
      ignore_errors: yes

    # ===============================================
    # Remove SSM Agent Installation
    # ===============================================
    - name: Check if SSM Agent is installed via snap
      command: snap list amazon-ssm-agent
      register: snap_check
      failed_when: false
      changed_when: false

    - name: Remove SSM Agent snap package
      snap:
        name: amazon-ssm-agent
        state: absent
      when: snap_check.rc == 0
      ignore_errors: yes

    - name: Check if SSM Agent is installed via package manager
      package_facts:
        manager: auto

    - name: Remove SSM Agent package (apt/deb)
      apt:
        name: amazon-ssm-agent
        state: absent
        purge: yes
      when: ansible_facts.packages['amazon-ssm-agent'] is defined
      ignore_errors: yes

    # ===============================================
    # Remove SSM Configuration and Data
    # ===============================================
    - name: Remove SSM configuration directories
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/amazon/ssm
        - /var/lib/amazon/ssm
        - /var/log/amazon/ssm
        - /opt/aws/ssm
        - /tmp/ssm
      ignore_errors: yes

    - name: Remove SSM systemd service files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/systemd/system/amazon-ssm-agent.service
        - /lib/systemd/system/amazon-ssm-agent.service
        - /usr/lib/systemd/system/amazon-ssm-agent.service
      ignore_errors: yes

    - name: Reload systemd daemon
      systemd:
        daemon_reload: yes

    # ===============================================
    # Optional: Remove AWS CLI (if installed by SSM playbook)
    # ===============================================
    - name: Check if AWS CLI was installed in /usr/local/bin
      stat:
        path: /usr/local/bin/aws
      register: aws_cli_local

    - name: Display AWS CLI status
      debug:
        msg: |
          AWS CLI v2 found in /usr/local/bin/aws
          Skipping removal - remove manually if needed:
          sudo rm -rf /usr/local/bin/aws /usr/local/bin/aws_completer /usr/local/aws-cli
      when: aws_cli_local.stat.exists

    # ===============================================
    # Clean up any remaining processes
    # ===============================================
    - name: Kill any remaining SSM processes
      shell: |
        pkill -f amazon-ssm-agent || true
        pkill -f ssm-agent-worker || true
      ignore_errors: yes

    # ===============================================
    # Verification
    # ===============================================
    - name: Verify SSM Agent removal
      block:
        - name: Check for SSM processes
          shell: pgrep -f amazon-ssm-agent
          register: ssm_processes
          failed_when: false
          changed_when: false

        - name: Check for SSM snap package
          command: snap list amazon-ssm-agent
          register: snap_verify
          failed_when: false
          changed_when: false

        - name: Check for SSM package
          shell: dpkg -l | grep amazon-ssm-agent
          register: package_verify
          failed_when: false
          changed_when: false

        - name: Check for SSM directories
          stat:
            path: "{{ item }}"
          register: dir_check
          loop:
            - /etc/amazon/ssm
            - /var/lib/amazon/ssm
            - /var/log/amazon/ssm

    - name: Display cleanup results
      debug:
        msg: |
          🧹 SSM Cleanup Results:
          
          Running processes: {{ 'None found' if ssm_processes.rc != 0 else 'Still running!' }}
          Snap package: {{ 'Removed' if snap_verify.rc != 0 else 'Still installed!' }}
          System package: {{ 'Removed' if package_verify.rc != 0 else 'Still installed!' }}
          Config directories: {{ 'Removed' if dir_check.results | selectattr('stat.exists') | list | length == 0 else 'Some remain' }}

    - name: Final cleanup summary
      debug:
        msg: |
          ✅ SSM Agent Cleanup Complete!
          
          Hostname: {{ inventory_hostname }}
          Status: Server removed from AWS SSM management
          
          {% if ssm_processes.rc == 0 or snap_verify.rc == 0 or package_verify.rc == 0 %}
          ⚠️  Warning: Some SSM components may still be present
          You may need to manually remove remaining items or reboot the server
          {% else %}
          ✅ All SSM components successfully removed
          {% endif %}
          
          Next Steps:
          1. Server is now clean and ready for fresh SSM registration
          2. Run the SSM registration playbook to re-register if needed
          3. Verify the server no longer appears in AWS Systems Manager console
