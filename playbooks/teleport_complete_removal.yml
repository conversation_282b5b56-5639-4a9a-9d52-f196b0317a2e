---
- name: Complete Teleport Removal Playbook
  hosts: "{{ target_hosts | default('gunnedah') }}"
  become: true
  gather_facts: true
  strategy: linear

  vars:
    # Set to true to force removal even if processes are running
    force_removal: true
    # Set to true to remove Docker containers/images related to Teleport
    remove_docker_artifacts: true
    # Set to true to remove user accounts created for Teleport
    remove_teleport_user: true

  tasks:
    - name: Check if Teleport binary exists
      stat:
        path: /usr/local/bin/teleport
      register: teleport_binary

    - name: Check if Teleport package is installed
      shell: dpkg -l | grep -i teleport || echo "not_found"
      register: teleport_package_check
      changed_when: false
      failed_when: false

    - name: Get current Teleport version (if installed)
      shell: teleport version 2>/dev/null | grep ^Teleport | awk '{print $2}' || echo "none"
      register: current_teleport_version
      changed_when: false
      failed_when: false
      when: teleport_binary.stat.exists

    - name: Display current Teleport status
      debug:
        msg: |
          Teleport binary exists: {{ teleport_binary.stat.exists }}
          Current version: {{ current_teleport_version.stdout | default('N/A') }}
          Package installed: {{ 'Yes' if 'teleport' in teleport_package_check.stdout else 'No' }}

    - name: Check for running Teleport processes
      shell: pgrep -f teleport || echo "no_processes"
      register: teleport_processes
      changed_when: false
      failed_when: false

    - name: Display running Teleport processes
      debug:
        msg: "Running Teleport processes: {{ teleport_processes.stdout_lines if teleport_processes.stdout != 'no_processes' else 'None' }}"

    - name: Fail if Teleport processes are running and force_removal is false
      fail:
        msg: |
          Teleport processes are still running. Set force_removal=true to proceed anyway.
          Running processes: {{ teleport_processes.stdout_lines }}
      when:
        - teleport_processes.stdout != "no_processes"
        - not (force_removal | bool)

    # ===== STOP SERVICES =====
    - name: Stop Teleport systemd service
      systemd:
        name: teleport
        state: stopped
        enabled: false
        daemon_reload: true
      ignore_errors: true

    - name: Kill any remaining Teleport processes
      shell: |
        if pgrep -f teleport > /dev/null 2>&1; then
          pkill -f teleport
          sleep 2
        fi
      ignore_errors: true
      when: force_removal | bool

    - name: Wait for processes to terminate
      wait_for:
        timeout: 5
      when: force_removal | bool

    - name: Force kill any stubborn Teleport processes
      shell: |
        if pgrep -f teleport > /dev/null 2>&1; then
          pkill -9 -f teleport
          sleep 1
        fi
      ignore_errors: true
      when: force_removal | bool

    # ===== REMOVE DOCKER ARTIFACTS =====
    - name: Stop and remove Teleport Docker containers
      shell: |
        # Stop all containers with teleport in the name
        docker ps -a --filter "name=teleport" --format "{{.Names}}" | xargs -r docker stop
        docker ps -a --filter "name=teleport" --format "{{.Names}}" | xargs -r docker rm

        # Stop containers using teleport images
        docker ps -a --filter "ancestor=teleport" --format "{{.Names}}" | xargs -r docker stop
        docker ps -a --filter "ancestor=teleport" --format "{{.Names}}" | xargs -r docker rm
      ignore_errors: true
      when:
        - remove_docker_artifacts | bool
        - ansible_facts['services']['docker.service'] is defined
        - ansible_facts['services']['docker.service']['state'] == 'running'

    - name: Remove Teleport Docker images
      shell: |
        # Remove teleport images
        docker images --filter "reference=teleport*" --format "{{.Repository}}:{{.Tag}}" | xargs -r docker rmi -f
        docker images --filter "reference=*teleport*" --format "{{.Repository}}:{{.Tag}}" | xargs -r docker rmi -f
      ignore_errors: true
      when:
        - remove_docker_artifacts | bool
        - ansible_facts['services']['docker.service'] is defined
        - ansible_facts['services']['docker.service']['state'] == 'running'

    # ===== REMOVE PACKAGES AND BINARIES =====
    - name: Remove Teleport APT package
      apt:
        name: teleport
        state: absent
        purge: true
      ignore_errors: true

    - name: Remove Teleport binary and related files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /usr/local/bin/teleport
        - /usr/local/bin/tctl
        - /usr/local/bin/tsh
        - /usr/bin/teleport
        - /usr/bin/tctl
        - /usr/bin/tsh

    # ===== REMOVE SYSTEMD SERVICE FILES =====
    - name: Remove Teleport systemd service files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/systemd/system/teleport.service
        - /lib/systemd/system/teleport.service
        - /usr/lib/systemd/system/teleport.service
      notify: reload systemd

    # ===== REMOVE CONFIGURATION FILES =====
    - name: Remove Teleport configuration files
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /etc/teleport.yaml
        - /etc/teleport
        - /etc/default/teleport

    # ===== REMOVE DATA DIRECTORIES =====
    - name: Remove Teleport data directories
      file:
        path: "{{ item }}"
        state: absent
      loop:
        - /var/lib/teleport
        - /opt/teleport
        - /var/log/teleport
        - /run/teleport.pid

    # ===== REMOVE LOG ROTATION CONFIG =====
    - name: Remove Teleport logrotate configuration
      file:
        path: /etc/logrotate.d/teleport
        state: absent

    # ===== REMOVE TELEPORT USER =====
    - name: Remove teleport system user
      user:
        name: teleport
        state: absent
        remove: true
        force: true
      when: remove_teleport_user | bool
      ignore_errors: true

    - name: Remove teleport group
      group:
        name: teleport
        state: absent
      when: remove_teleport_user | bool
      ignore_errors: true

    # ===== CLEANUP TEMPORARY FILES =====
    - name: Remove temporary Teleport installation files
      find:
        paths: /tmp
        patterns: "teleport*"
        file_type: any
      register: temp_teleport_files

    - name: Delete temporary Teleport files
      file:
        path: "{{ item.path }}"
        state: absent
      loop: "{{ temp_teleport_files.files }}"
      ignore_errors: true

    # ===== VERIFICATION =====
    - name: Verify Teleport removal
      block:
        - name: Check if Teleport binary still exists
          stat:
            path: /usr/local/bin/teleport
          register: teleport_binary_check

        - name: Check if Teleport package is still installed
          shell: dpkg -l | grep -i teleport || echo "not_found"
          register: teleport_package_final_check
          changed_when: false
          failed_when: false

        - name: Check for remaining Teleport processes
          shell: pgrep -f teleport || echo "no_processes"
          register: teleport_processes_final
          changed_when: false
          failed_when: false

        - name: Check for remaining Teleport files
          find:
            paths:
              - /etc
              - /var/lib
              - /opt
              - /var/log
            patterns: "*teleport*"
            file_type: any
            recurse: false
          register: remaining_teleport_files

        - name: Display removal results
          debug:
            msg: |
              ✅ Teleport Removal Complete!
              
              Binary removed: {{ 'Yes' if not teleport_binary_check.stat.exists else 'No - Still exists!' }}
              Package removed: {{ 'Yes' if 'not_found' in teleport_package_final_check.stdout else 'No - Still installed!' }}
              Processes stopped: {{ 'Yes' if teleport_processes_final.stdout == 'no_processes' else 'No - Still running!' }}
              Remaining files: {{ remaining_teleport_files.files | length }}
              
              {% if remaining_teleport_files.files | length > 0 %}
              Remaining files found:
              {% for file in remaining_teleport_files.files %}
              - {{ file.path }}
              {% endfor %}
              {% endif %}

        - name: Warn about remaining files
          debug:
            msg: "⚠️  Warning: Some Teleport-related files may still exist. Review the list above."
          when: remaining_teleport_files.files | length > 0

  handlers:
    - name: reload systemd
      systemd:
        daemon_reload: true
