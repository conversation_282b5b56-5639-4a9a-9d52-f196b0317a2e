all:
  children:
    infrastructure:
      children:
        icinga2_satellite:
          hosts: icinga2-satellite.pop.infra.fingermark.tech
          vars:
            icinga2_satellite_endpoint: api.icinga2-master.infra.fingermark.tech
            softether_client_private_ip: **********
    production:
      children:
        albany:
          hosts:
            fm-pop-nzl-03013.eyeq.vpn: {}
          vars:
            address: 290 Oteha Valley Road, Albany Auckland 0632
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Albany
            id: '03013'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0QP6V7
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03013
            timezone: Pacific/Auckland
        east_tamaki:
          hosts:
            fm-pop-nzl-03002.eyeq.vpn: {}
          vars:
            address: 68 East Tamaki Road, Papatoetoe, auckland
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: East Tamaki
            id: '03002'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0J961P
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03002
            timezone: Pacific/Auckland
        hastings:
          hosts:
            fm-pop-nzl-03005.eyeq.vpn: {}
          vars:
            address: 710 Heretaunga Street Hastings
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Hastings
            id: '03005'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0HV5PF
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03005
            timezone: Pacific/Auckland
        # invercargill:
        #   hosts:
        #     fm-pop-nzl-03011.eyeq.vpn: {}
        #   vars:
        #     address: 260 Dee Street, Invercargill
        #     country: nzl
        #     country_code: '554'
        #     customer: pop
        #     deployment_env: Production
        #     display_name: Invercargill
        #     id: '03011'
        #     latitude: '-41.8294535363'
        #     longitude: '171.462581036'
        #     serial_number: GM0V5XCE
        #     site_contact_email: <EMAIL>
        #     site_id: fm-pop-nzl-03011
        #     timezone: Pacific/Auckland
        lower_hutt:
          hosts:
            fm-pop-nzl-03007.eyeq.vpn: {}
          vars:
            address: 367 High Street Lower Hutt
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Lower Hutt
            id: '03007'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0HV5PH
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03007
            timezone: Pacific/Auckland
        popeyes_takanini:
          hosts:
            fm-pop-nzl-03001.eyeq.vpn: {}
          vars:
            address: 106 Great South Road, Takanini, Auckland 2112
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Popeyes Takanini
            id: '03001'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: PC2CJXYZ
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03001
            timezone: Pacific/Auckland
        popeyes_taupo:
          hosts:
            fm-pop-nzl-03006.eyeq.vpn: {}
          vars:
            address: 189 Napier road, Taupo, 3379
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Staging
            display_name: Popeyes Taupo
            id: '03006'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0HV5PG
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03006
            timezone: Pacific/Auckland
        rangitikei:
          hosts:
            fm-pop-nzl-03012.eyeq.vpn: {}
          vars:
            address: 403-413 Rangitikei Street, Palmerston North
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Rangitikei
            id: '03012'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0QP6V8
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03012
            timezone: Pacific/Auckland
        station_road:
          hosts:
            fm-pop-nzl-03009.eyeq.vpn: {}
          vars:
            address: 49A Station Road Papatoetoe
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Station Road
            id: 03009
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0QP6V9
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03009
            timezone: Pacific/Auckland
        te_rapa:
          hosts:
            fm-pop-nzl-03010.eyeq.vpn: {}
          vars:
            address: 675 Te Rapa Road
            country: nzl
            country_code: '554'
            customer: pop
            deployment_env: Production
            display_name: Te Rapa
            id: '03010'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0QP6V6
            site_contact_email: <EMAIL>
            site_id: fm-pop-nzl-03010
            timezone: Pacific/Auckland
        test:
          hosts: fm-pop-nzl-00000.eyeq.vpn
          vars:
            display_name: Test
            timezone: Pacific/Auckland
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-pop-nzl-helm
        aws_s3_bucket_camera_images: eyecue-pop-us-camera-images
        icinga2_satellite_endpoint: icinga2-satellite.pop.infra.fingermark.tech
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: pop-nzl
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
