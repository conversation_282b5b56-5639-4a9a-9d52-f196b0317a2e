---
# OpenVPN Client Handlers

- name: reload systemd
  systemd:
    daemon_reload: true

- name: restart openvpn-client
  systemd:
    name: "{{ openvpn_client_service_name }}"
    state: restarted
  when: ansible_service_mgr == "systemd"

- name: start openvpn-client
  systemd:
    name: "{{ openvpn_client_service_name }}"
    state: started
  when: ansible_service_mgr == "systemd"

- name: stop openvpn-client
  systemd:
    name: "{{ openvpn_client_service_name }}"
    state: stopped
  when: ansible_service_mgr == "systemd"
