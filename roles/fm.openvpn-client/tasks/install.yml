---
# Install OpenVPN Client

- name: Update package cache (Debian/Ubuntu)
  apt:
    update_cache: true
    cache_valid_time: 3600
  when: ansible_os_family == "Debian"

- name: Install OpenVPN and dependencies (Debian/Ubuntu)
  apt:
    name:
      - openvpn
      - openssl
      - ca-certificates
      - curl
      - dnsutils
      - iptables
      - net-tools
    state: present
  when: ansible_os_family == "Debian"

- name: Install OpenVPN and dependencies (RedHat/CentOS)
  yum:
    name:
      - openvpn
      - openssl
      - ca-certificates
      - curl
      - bind-utils
      - iptables
      - net-tools
    state: present
  when: ansible_os_family == "RedHat"

- name: Create OpenVPN client configuration directory
  file:
    path: "{{ openvpn_client_config_dir }}"
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Create OpenVPN log directory
  file:
    path: "{{ openvpn_client_log_dir }}"
    state: directory
    owner: root
    group: root
    mode: '0755'

- name: Ensure OpenVPN service is stopped (we'll use our custom service)
  systemd:
    name: openvpn
    state: stopped
    enabled: false
  failed_when: false
  when: ansible_service_mgr == "systemd"
