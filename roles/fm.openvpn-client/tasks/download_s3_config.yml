---
# Download OpenVPN client configuration from S3

- name: Check if AWS CLI is available
  command: which aws
  register: aws_cli_check
  failed_when: false
  changed_when: false

- name: Install AWS CLI if not present
  package:
    name: awscli
    state: present
  when: aws_cli_check.rc != 0

- name: Set client config S3 key (using generic client.ovpn)
  set_fact:
    client_config_s3_key: "clients/client.ovpn"

- name: Download client configuration from S3 using AWS CLI (uses server's IAM role)
  command: >
    aws s3 cp
    s3://{{ vpn_config_s3_bucket }}/{{ client_config_s3_key }}
    /tmp/client.ovpn
    --region {{ vpn_config_s3_region | default('ap-southeast-2') }}
  register: s3_download_result
  changed_when: true

- name: Validate downloaded configuration file
  stat:
    path: "/tmp/client.ovpn"
  register: downloaded_config_stat

- name: Ensure downloaded config is valid
  assert:
    that:
      - downloaded_config_stat.stat.exists
      - downloaded_config_stat.stat.size > 0
    fail_msg: "Downloaded client configuration is empty or does not exist"

- name: Set configuration content from downloaded file
  slurp:
    src: "/tmp/client.ovpn"
  register: downloaded_config_content

- name: Set openvpn_client_config_content variable
  set_fact:
    openvpn_client_config_content: "{{ downloaded_config_content.content | b64decode }}"

- name: Clean up temporary file
  file:
    path: "/tmp/client.ovpn"
    state: absent

- name: Log successful S3 download
  debug:
    msg: "Successfully downloaded VPN configuration from s3://{{ vpn_config_s3_bucket }}/{{ client_config_s3_key }}"
