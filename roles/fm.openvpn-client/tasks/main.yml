---
# OpenVPN Client Installation and Configuration

- name: Include OS-specific variables
  include_vars: "{{ ansible_os_family }}.yml"
  failed_when: false

- name: Validate required variables
  assert:
    that:
      - openvpn_server_hostname is defined
      - openvpn_server_hostname | length > 0
    fail_msg: "Required variable openvpn_server_hostname must be defined"

- name: Download client configuration from S3 (if S3 config provided)
  include_tasks: download_s3_config.yml
  when:
    - vpn_config_s3_bucket is defined
    - vpn_config_s3_bucket | length > 0

- name: Check if client.ovpn file exists in role files (fallback)
  stat:
    path: "{{ role_path }}/files/client.ovpn"
  register: client_ovpn_file
  delegate_to: localhost
  become: false
  run_once: true
  when: vpn_config_s3_bucket is not defined or vpn_config_s3_bucket | length == 0

- name: Validate client.ovpn file exists (fallback)
  assert:
    that:
      - client_ovpn_file.stat.exists
    fail_msg: "client.ovpn file not found in {{ role_path }}/files/client.ovpn"
  when: vpn_config_s3_bucket is not defined or vpn_config_s3_bucket | length == 0

- name: Install OpenVPN client
  include_tasks: install.yml

- name: Configure network interfaces
  include_tasks: configure_network.yml

- name: Configure OpenVPN client
  include_tasks: configure_client.yml

- name: Create management scripts
  include_tasks: create_scripts.yml

- name: Configure systemd service
  include_tasks: configure_service.yml

- name: Start and enable service if auto_start is enabled
  systemd:
    name: "{{ openvpn_client_service_name }}"
    enabled: "{{ openvpn_client_auto_start }}"
    state: "{{ 'started' if openvpn_client_auto_start else 'stopped' }}"
    daemon_reload: true
  when: ansible_service_mgr == "systemd"

- name: Enable and start VPN connectivity watchdog
  systemd:
    name: "openvpn-watchdog"
    enabled: "{{ openvpn_client_enable_watchdog }}"
    state: "{{ 'started' if openvpn_client_enable_watchdog else 'stopped' }}"
    daemon_reload: true
  when:
    - ansible_service_mgr == "systemd"
    - openvpn_client_enable_watchdog | default(true)
