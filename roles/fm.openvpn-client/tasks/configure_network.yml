---
# Configure Network Interface

- name: Gather network interface facts
  setup:
    gather_subset:
      - network

- name: Debug network interface configuration
  debug:
    msg:
      - "Primary wired interface: {{ openvpn_client_wired_interface }}"
      - "Available interfaces: {{ ansible_interfaces }}"
      - "Default interface: {{ ansible_default_ipv4.interface }}"

- name: Verify primary wired interface exists
  fail:
    msg: "Primary wired interface {{ openvpn_client_wired_interface }} not found. Available interfaces: {{ ansible_interfaces }}"
  when: openvpn_client_wired_interface not in ansible_interfaces

- name: Get wired interface IP address
  set_fact:
    openvpn_client_wired_ip: "{{ hostvars[inventory_hostname]['ansible_' + openvpn_client_wired_interface]['ipv4']['address'] }}"
  when:
    - hostvars[inventory_hostname]['ansible_' + openvpn_client_wired_interface] is defined
    - hostvars[inventory_hostname]['ansible_' + openvpn_client_wired_interface]['ipv4'] is defined

- name: Get default gateway
  set_fact:
    openvpn_client_gateway: "{{ ansible_default_ipv4.gateway | default('') }}"
  when: ansible_default_ipv4.gateway is defined

- name: Get gateway from route table if default not available
  shell: ip route show | grep "^default" | grep "{{ openvpn_client_wired_interface }}" | awk '{print $3}' | head -1
  register: route_gateway
  when: ansible_default_ipv4.gateway is not defined or openvpn_client_gateway == ""

- name: Set gateway from route command
  set_fact:
    openvpn_client_gateway: "{{ route_gateway.stdout }}"
  when:
    - route_gateway is defined
    - route_gateway.stdout is defined
    - route_gateway.stdout != ""

- name: Display network configuration
  debug:
    msg:
      - "Interface: {{ openvpn_client_wired_interface }}"
      - "IP Address: {{ openvpn_client_wired_ip | default('not configured') }}"
      - "Gateway: {{ openvpn_client_gateway | default('not detected') }}"
