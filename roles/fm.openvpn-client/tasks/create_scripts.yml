---
# Create VPN Management Scripts

- name: Create VPN management script
  template:
    src: vpn-manager.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-manager.sh"
    owner: root
    group: root
    mode: '0755'
    backup: true

- name: Create VPN connection check script
  template:
    src: vpn-check.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-check.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN status monitoring script
  template:
    src: vpn-monitor.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-monitor.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN pre-start script
  template:
    src: vpn-pre-start.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-pre-start.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN post-stop script
  template:
    src: vpn-post-stop.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-post-stop.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN troubleshooting script
  template:
    src: vpn-troubleshoot.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-troubleshoot.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN connectivity watchdog script
  template:
    src: vpn-watchdog.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-watchdog.sh"
    owner: root
    group: root
    mode: '0755'

- name: Create VPN performance testing script
  template:
    src: vpn-performance-test.sh.j2
    dest: "{{ openvpn_client_script_dir }}/vpn-performance-test.sh"
    owner: root
    group: root
    mode: '0755'
  when: openvpn_client_enable_performance_testing | default(false)

- name: Create convenient symlinks for VPN management
  file:
    src: "{{ openvpn_client_script_dir }}/vpn-manager.sh"
    dest: "/usr/local/bin/{{ item }}"
    state: link
    force: true
  loop:
    - vpn-connect
    - vpn-disconnect
    - vpn-status

- name: Create emergency recovery cron job (safety net)
  cron:
    name: "VPN Emergency Recovery Check"
    minute: "*/5"
    job: "/usr/local/bin/vpn-watchdog.sh test >/dev/null 2>&1 || /usr/local/bin/vpn-watchdog.sh emergency >/dev/null 2>&1"
    user: root
    state: present
  when: openvpn_client_enable_watchdog | default(true)
