---
# Configure OpenVPN Client

- name: Create OpenVPN client configuration file (from role files)
  copy:
    src: client.ovpn
    dest: "{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
    owner: root
    group: root
    mode: '0600'
    backup: true
  notify: restart openvpn-client
  when: openvpn_client_config_content is not defined

- name: Create OpenVPN client configuration file (from variable content)
  copy:
    content: "{{ openvpn_client_config_content }}"
    dest: "{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
    owner: root
    group: root
    mode: '0600'
    backup: true
  notify: restart openvpn-client
  when: openvpn_client_config_content is defined

- name: Validate OpenVPN configuration file syntax
  command: openvpn --config "{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}" --verb 1 --show-ciphers
  register: openvpn_config_test
  failed_when: false
  changed_when: false

- name: Check if configuration file is readable
  stat:
    path: "{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
  register: config_file_stat

- name: Validate configuration file exists and is readable
  assert:
    that:
      - config_file_stat.stat.exists
      - config_file_stat.stat.readable
    fail_msg: "OpenVPN configuration file is not readable or does not exist"

- name: Create OpenVPN client auth-user-pass file (if needed)
  copy:
    content: |
      {{ openvpn_client_username | default('') }}
      {{ openvpn_client_password | default('') }}
    dest: "{{ openvpn_client_config_dir }}/auth-user-pass"
    owner: root
    group: root
    mode: '0600'
  when: 
    - openvpn_client_username is defined
    - openvpn_client_password is defined
  notify: restart openvpn-client

- name: Set up log rotation for OpenVPN logs
  copy:
    content: |
      {{ openvpn_client_log_dir }}/*.log {
          daily
          missingok
          rotate 7
          compress
          delaycompress
          notifempty
          copytruncate
      }
    dest: /etc/logrotate.d/openvpn-client
    owner: root
    group: root
    mode: '0644'
