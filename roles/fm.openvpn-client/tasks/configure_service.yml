---
# Configure Systemd Service

- name: Create systemd service file for OpenVPN client
  template:
    src: openvpn-client.service.j2
    dest: "/etc/systemd/system/{{ openvpn_client_service_name }}.service"
    owner: root
    group: root
    mode: '0644'
    backup: true
  notify: 
    - reload systemd
    - restart openvpn-client

- name: Create systemd timer for VPN monitoring (optional)
  template:
    src: openvpn-monitor.timer.j2
    dest: "/etc/systemd/system/openvpn-monitor.timer"
    owner: root
    group: root
    mode: '0644'
  when: openvpn_client_enable_monitoring | default(false)

- name: Create systemd service for VPN monitoring
  template:
    src: openvpn-monitor.service.j2
    dest: "/etc/systemd/system/openvpn-monitor.service"
    owner: root
    group: root
    mode: '0644'
  when: openvpn_client_enable_monitoring | default(false)

- name: Create systemd service for VPN connectivity watchdog
  template:
    src: openvpn-watchdog.service.j2
    dest: "/etc/systemd/system/openvpn-watchdog.service"
    owner: root
    group: root
    mode: '0644'
    backup: true
  notify:
    - reload systemd

- name: Force systemd to reload daemon
  systemd:
    daemon_reload: true
