#!/bin/bash
# OpenVPN Connection Health Check Script
# Generated by Ansible - DO NOT EDIT MANUALLY

LOG_FILE="{{ openvpn_client_log_dir }}/health-check.log"
SERVICE_NAME="{{ openvpn_client_service_name }}"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Check if VPN service is running
check_service() {
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        return 0
    else
        return 1
    fi
}

# Check if tunnel interface exists and has IP
check_tunnel() {
    if ip addr show tun0 >/dev/null 2>&1; then
        VPN_IP=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)
        if [ -n "$VPN_IP" ]; then
            return 0
        fi
    fi
    return 1
}

# Check internet connectivity through VPN
check_connectivity() {
    # Try to reach a reliable external service
    if curl -s --max-time 10 --interface tun0 https://******* >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Check if public IP matches expected NAT Gateway IPs
check_public_ip() {
    PUBLIC_IP=$(curl -s --max-time 10 ifconfig.me 2>/dev/null)
    if [ -n "$PUBLIC_IP" ]; then
        # Log the current public IP for monitoring
        log "Current public IP: $PUBLIC_IP"
        return 0
    else
        return 1
    fi
}

# Main health check
main() {
    local exit_code=0
    
    log "Starting VPN health check"
    
    # Check service status
    if check_service; then
        log "✓ OpenVPN service is running"
    else
        log "✗ OpenVPN service is not running"
        exit_code=1
    fi
    
    # Check tunnel interface
    if check_tunnel; then
        VPN_IP=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)
        log "✓ VPN tunnel is active (IP: $VPN_IP)"
    else
        log "✗ VPN tunnel is not active"
        exit_code=1
    fi
    
    # Check connectivity
    if check_connectivity; then
        log "✓ Internet connectivity through VPN is working"
    else
        log "✗ Internet connectivity through VPN failed"
        exit_code=1
    fi
    
    # Check public IP
    if check_public_ip; then
        log "✓ Public IP check completed"
    else
        log "✗ Unable to determine public IP"
        exit_code=1
    fi
    
    if [ $exit_code -eq 0 ]; then
        log "✓ VPN health check passed"
    else
        log "✗ VPN health check failed"
        
        # Optionally restart VPN service if health check fails
        if [ "${1:-}" = "--auto-restart" ]; then
            log "Attempting to restart VPN service"
            systemctl restart "$SERVICE_NAME"
            sleep 10
            
            # Re-run basic checks
            if check_service && check_tunnel; then
                log "✓ VPN service restarted successfully"
                exit_code=0
            else
                log "✗ VPN service restart failed"
            fi
        fi
    fi
    
    exit $exit_code
}

# Run the health check
main "$@"
