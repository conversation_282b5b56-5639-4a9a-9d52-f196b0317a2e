[Unit]
Description=OpenVPN Connectivity Watchdog
After=network-online.target {{ openvpn_client_service_name }}.service
Wants=network-online.target
Documentation=man:openvpn(8)

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/local/bin/vpn-watchdog.sh monitor
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=30s
TimeoutStopSec=30s

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=openvpn-watchdog

# Security settings
NoNewPrivileges=true
ProtectSystem=false
ProtectHome=true
PrivateTmp=true
CapabilityBoundingSet=CAP_NET_ADMIN CAP_NET_RAW CAP_SYS_ADMIN

# Allow network access for connectivity testing
RestrictAddressFamilies=AF_UNIX AF_INET AF_INET6
PrivateDevices=false

[Install]
WantedBy=multi-user.target
