#!/bin/bash
# OpenVPN Connectivity Watchdog
# Generated by Ansible - DO NOT EDIT MANUALLY
#
# This script monitors internet connectivity and automatically recovers
# from VPN failures to ensure remote servers maintain internet access

# Configuration
SERVICE_NAME="{{ openvpn_client_service_name }}"
LOG_FILE="{{ openvpn_client_log_dir }}/watchdog.log"
STATE_FILE="/var/lib/openvpn/watchdog-state"
BACKUP_ROUTE_FILE="/var/lib/openvpn/backup-route.conf"
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"

# Connectivity test settings
CONNECTIVITY_TIMEOUT=10
CONNECTIVITY_RETRIES=3
RESTART_ATTEMPTS=3
EMERGENCY_RECOVERY_THRESHOLD=5

# Create directories
mkdir -p "$(dirname "$LOG_FILE")"
mkdir -p "$(dirname "$STATE_FILE")"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Load/save watchdog state
load_state() {
    if [ -f "$STATE_FILE" ]; then
        source "$STATE_FILE"
    else
        CONSECUTIVE_FAILURES=0
        RESTART_COUNT=0
        EMERGENCY_MODE=false
        LAST_RECOVERY_ATTEMPT=0
    fi
}

save_state() {
    cat > "$STATE_FILE" << EOF
CONSECUTIVE_FAILURES=$CONSECUTIVE_FAILURES
RESTART_COUNT=$RESTART_COUNT
EMERGENCY_MODE=$EMERGENCY_MODE
LAST_RECOVERY_ATTEMPT=$LAST_RECOVERY_ATTEMPT
EOF
}

# Test internet connectivity
test_connectivity() {
    local test_urls=("8.8.8.8" "1.1.1.1" "**************")
    local success=false
    
    for url in "${test_urls[@]}"; do
        if timeout "$CONNECTIVITY_TIMEOUT" curl -s --max-time "$CONNECTIVITY_TIMEOUT" "http://$url" >/dev/null 2>&1; then
            success=true
            break
        fi
    done
    
    if $success; then
        return 0
    else
        return 1
    fi
}

# Check if VPN service is running
check_vpn_service() {
    systemctl is-active --quiet "$SERVICE_NAME"
}

# Check if VPN tunnel is up
check_vpn_tunnel() {
    ip addr show tun0 >/dev/null 2>&1
}

# Restart VPN service
restart_vpn() {
    log "Attempting to restart VPN service (attempt $((RESTART_COUNT + 1))/$RESTART_ATTEMPTS)"
    
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    sleep 5
    systemctl start "$SERVICE_NAME"
    
    # Wait for connection to establish
    local wait_time=30
    for i in $(seq 1 $wait_time); do
        if check_vpn_tunnel && test_connectivity; then
            log "VPN service restarted successfully"
            RESTART_COUNT=0
            CONSECUTIVE_FAILURES=0
            save_state
            return 0
        fi
        sleep 2
    done
    
    RESTART_COUNT=$((RESTART_COUNT + 1))
    log "VPN restart attempt $RESTART_COUNT failed"
    save_state
    return 1
}

# Emergency recovery - restore direct internet access
emergency_recovery() {
    log "EMERGENCY: Activating direct internet access"
    EMERGENCY_MODE=true
    LAST_RECOVERY_ATTEMPT=$(date +%s)
    
    # Stop VPN service
    systemctl stop "$SERVICE_NAME" 2>/dev/null || true
    
    # Restore original default route
    if [ -f "$BACKUP_ROUTE_FILE" ]; then
        source "$BACKUP_ROUTE_FILE"
        
        if [ -n "$BACKUP_GATEWAY" ] && [ -n "$BACKUP_INTERFACE" ]; then
            # Remove any VPN-related routes
            ip route del default 2>/dev/null || true
            
            # Add original route back
            if ip route add default via "$BACKUP_GATEWAY" dev "$BACKUP_INTERFACE" 2>/dev/null; then
                log "EMERGENCY: Restored direct internet via $BACKUP_GATEWAY"
                
                # Test if emergency recovery worked
                if test_connectivity; then
                    log "EMERGENCY: Direct internet access confirmed"
                    save_state
                    return 0
                fi
            fi
        fi
    fi
    
    log "EMERGENCY: Failed to restore direct internet access"
    save_state
    return 1
}

# Try to restore VPN from emergency mode
try_restore_vpn() {
    local current_time=$(date +%s)
    local time_since_recovery=$((current_time - LAST_RECOVERY_ATTEMPT))
    
    # Only try to restore VPN every 5 minutes in emergency mode
    if [ $time_since_recovery -lt 300 ]; then
        return 1
    fi
    
    log "Attempting to restore VPN from emergency mode"
    
    # Reset counters for fresh attempt
    RESTART_COUNT=0
    
    if restart_vpn; then
        log "Successfully restored VPN from emergency mode"
        EMERGENCY_MODE=false
        save_state
        return 0
    else
        log "Failed to restore VPN, remaining in emergency mode"
        LAST_RECOVERY_ATTEMPT=$current_time
        save_state
        return 1
    fi
}

# Main monitoring loop
monitor_connectivity() {
    log "Starting VPN connectivity monitoring"
    
    while true; do
        load_state
        
        # If in emergency mode, try to restore VPN periodically
        if $EMERGENCY_MODE; then
            if test_connectivity; then
                try_restore_vpn
            else
                log "EMERGENCY: No internet access even in emergency mode"
            fi
            sleep 60
            continue
        fi
        
        # Normal monitoring mode
        if test_connectivity; then
            # Internet is working
            if [ $CONSECUTIVE_FAILURES -gt 0 ]; then
                log "Internet connectivity restored"
                CONSECUTIVE_FAILURES=0
                RESTART_COUNT=0
                save_state
            fi
        else
            # Internet connectivity failed
            CONSECUTIVE_FAILURES=$((CONSECUTIVE_FAILURES + 1))
            log "Internet connectivity test failed (failure $CONSECUTIVE_FAILURES)"
            
            if [ $CONSECUTIVE_FAILURES -ge $CONNECTIVITY_RETRIES ]; then
                # Multiple failures detected
                if check_vpn_service && check_vpn_tunnel; then
                    log "VPN appears up but no internet - restarting VPN"
                    restart_vpn
                elif ! check_vpn_service; then
                    log "VPN service is down - restarting"
                    restart_vpn
                else
                    log "VPN tunnel is down - restarting VPN"
                    restart_vpn
                fi
                
                # If we've tried restarting multiple times, go to emergency mode
                if [ $RESTART_COUNT -ge $RESTART_ATTEMPTS ]; then
                    log "Multiple restart attempts failed - activating emergency recovery"
                    emergency_recovery
                fi
            fi
            
            save_state
        fi
        
        # Wait before next check
        sleep 30
    done
}

# Handle signals for clean shutdown
cleanup() {
    log "Watchdog shutting down"
    exit 0
}

trap cleanup SIGTERM SIGINT

# Main execution
case "${1:-monitor}" in
    "monitor")
        monitor_connectivity
        ;;
    "test")
        if test_connectivity; then
            echo "✓ Internet connectivity OK"
            exit 0
        else
            echo "✗ Internet connectivity FAILED"
            exit 1
        fi
        ;;
    "status")
        load_state
        echo "=== VPN Watchdog Status ==="
        echo "Emergency Mode: $EMERGENCY_MODE"
        echo "Consecutive Failures: $CONSECUTIVE_FAILURES"
        echo "Restart Count: $RESTART_COUNT"
        echo "Last Recovery Attempt: $(date -d "@$LAST_RECOVERY_ATTEMPT" 2>/dev/null || echo 'Never')"
        ;;
    "emergency")
        emergency_recovery
        ;;
    "restore")
        try_restore_vpn
        ;;
    *)
        echo "Usage: $0 {monitor|test|status|emergency|restore}"
        echo "  monitor   - Start continuous monitoring (default)"
        echo "  test      - Test internet connectivity once"
        echo "  status    - Show watchdog status"
        echo "  emergency - Force emergency recovery"
        echo "  restore   - Try to restore VPN from emergency mode"
        exit 1
        ;;
esac
