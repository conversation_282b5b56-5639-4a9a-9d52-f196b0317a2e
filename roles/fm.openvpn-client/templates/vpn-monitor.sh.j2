#!/bin/bash
# OpenVPN Monitoring Script
# Generated by Ansible - DO NOT EDIT MANUALLY

LOG_FILE="{{ openvpn_client_log_dir }}/monitor.log"
SERVICE_NAME="{{ openvpn_client_service_name }}"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Get VPN statistics
get_vpn_stats() {
    local stats_file="/tmp/vpn-stats.json"
    
    # Basic connection info
    local service_status="stopped"
    local tunnel_status="down"
    local vpn_ip=""
    local public_ip=""
    local uptime=""
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        service_status="running"
        
        if ip addr show tun0 >/dev/null 2>&1; then
            tunnel_status="up"
            vpn_ip=$(ip addr show tun0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)
        fi
        
        public_ip=$(curl -s --max-time 5 ifconfig.me 2>/dev/null || echo "unknown")
        
        # Get service uptime
        uptime=$(systemctl show "$SERVICE_NAME" --property=ActiveEnterTimestamp --value)
    fi
    
    # Network interface statistics
    local wired_rx_bytes=0
    local wired_tx_bytes=0
    local tunnel_rx_bytes=0
    local tunnel_tx_bytes=0
    
    if [ -f "/sys/class/net/{{ openvpn_client_wired_interface }}/statistics/rx_bytes" ]; then
        wired_rx_bytes=$(cat "/sys/class/net/{{ openvpn_client_wired_interface }}/statistics/rx_bytes")
        wired_tx_bytes=$(cat "/sys/class/net/{{ openvpn_client_wired_interface }}/statistics/tx_bytes")
    fi
    
    if [ -f "/sys/class/net/tun0/statistics/rx_bytes" ]; then
        tunnel_rx_bytes=$(cat "/sys/class/net/tun0/statistics/rx_bytes")
        tunnel_tx_bytes=$(cat "/sys/class/net/tun0/statistics/tx_bytes")
    fi
    
    # Create JSON output
    cat > "$stats_file" << EOF
{
    "timestamp": "$(date -Iseconds)",
    "service_status": "$service_status",
    "tunnel_status": "$tunnel_status",
    "vpn_ip": "$vpn_ip",
    "public_ip": "$public_ip",
    "uptime": "$uptime",
    "network_stats": {
        "wired_interface": {
            "name": "{{ openvpn_client_wired_interface }}",
            "rx_bytes": $wired_rx_bytes,
            "tx_bytes": $wired_tx_bytes
        },
        "tunnel_interface": {
            "name": "tun0",
            "rx_bytes": $tunnel_rx_bytes,
            "tx_bytes": $tunnel_tx_bytes
        }
    }
}
EOF
    
    echo "$stats_file"
}

# Monitor mode - continuous monitoring
monitor_continuous() {
    log "Starting continuous VPN monitoring"
    
    while true; do
        local stats_file=$(get_vpn_stats)
        
        # Extract key metrics for logging
        local service_status=$(jq -r '.service_status' "$stats_file")
        local tunnel_status=$(jq -r '.tunnel_status' "$stats_file")
        local public_ip=$(jq -r '.public_ip' "$stats_file")
        
        log "Status: Service=$service_status, Tunnel=$tunnel_status, PublicIP=$public_ip"
        
        # Check for issues and alert
        if [ "$service_status" = "running" ] && [ "$tunnel_status" = "down" ]; then
            log "WARNING: Service running but tunnel is down"
        fi
        
        if [ "$service_status" = "stopped" ]; then
            log "WARNING: VPN service is not running"
        fi
        
        sleep 60  # Monitor every minute
    done
}

# Single check mode
monitor_once() {
    local stats_file=$(get_vpn_stats)
    
    if [ "${1:-}" = "--json" ]; then
        cat "$stats_file"
    else
        echo "=== VPN Monitoring Report ==="
        echo "Timestamp: $(jq -r '.timestamp' "$stats_file")"
        echo "Service Status: $(jq -r '.service_status' "$stats_file")"
        echo "Tunnel Status: $(jq -r '.tunnel_status' "$stats_file")"
        echo "VPN IP: $(jq -r '.vpn_ip' "$stats_file")"
        echo "Public IP: $(jq -r '.public_ip' "$stats_file")"
        echo "Uptime: $(jq -r '.uptime' "$stats_file")"
        echo ""
        echo "Network Statistics:"
        echo "  Wired Interface ({{ openvpn_client_wired_interface }}):"
        echo "    RX: $(jq -r '.network_stats.wired_interface.rx_bytes' "$stats_file") bytes"
        echo "    TX: $(jq -r '.network_stats.wired_interface.tx_bytes' "$stats_file") bytes"
        echo "  Tunnel Interface (tun0):"
        echo "    RX: $(jq -r '.network_stats.tunnel_interface.rx_bytes' "$stats_file") bytes"
        echo "    TX: $(jq -r '.network_stats.tunnel_interface.tx_bytes' "$stats_file") bytes"
    fi
    
    rm -f "$stats_file"
}

# Main script logic
case "${1:-once}" in
    "continuous"|"monitor")
        monitor_continuous
        ;;
    "once"|"check")
        monitor_once "${2:-}"
        ;;
    *)
        echo "Usage: $0 {once|continuous} [--json]"
        echo "  once       - Single monitoring check"
        echo "  continuous - Continuous monitoring (runs forever)"
        echo "  --json     - Output in JSON format (only with 'once')"
        exit 1
        ;;
esac
