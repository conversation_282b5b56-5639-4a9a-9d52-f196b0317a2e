#!/bin/bash
# VPN Performance Testing Script
# Generated by Ansible - DO NOT EDIT MANUALLY
# Safe version without emergency mode functionality

# Configuration
TEST_DURATION=30
PING_COUNT=20

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_result() {
    echo -e "${GREEN}$1${NC}"
}

# Test 1: Current Network State
test_current_state() {
    print_header "Current Network State"
    
    echo "VPN Service: $(systemctl is-active openvpn-client 2>/dev/null || echo 'not-found')"
    echo "Watchdog Service: $(systemctl is-active openvpn-watchdog 2>/dev/null || echo 'not-found')"
    echo "VPN Tunnel: $(ip addr show tun0 >/dev/null 2>&1 && echo 'UP' || echo 'DOWN')"
    echo "Public IP: $(curl -s --max-time 10 ifconfig.me 2>/dev/null || echo 'Unable to determine')"
    echo "VPN Server: {{ openvpn_server_hostname }}"
    echo "Default Routes:"
    ip route show default | head -3
    echo ""
}

# Test 2: Latency Performance (using TCP since ICMP is blocked)
test_latency() {
    print_header "Latency Tests (TCP-based - ICMP blocked)"
    
    # Use TCP connections to common ports since ICMP ping is blocked
    local targets=(
        "*******:53"      # Cloudflare DNS
        "*******:53"      # Google DNS
        "google.com:80"   # Google HTTP
        "cloudflare.com:80" # Cloudflare HTTP
        "github.com:443"  # GitHub HTTPS
    )
    
    for target in "${targets[@]}"; do
        local host=$(echo "$target" | cut -d: -f1)
        local port=$(echo "$target" | cut -d: -f2)
        echo -n "Testing $host:$port: "
        
        # Measure TCP connection time
        local start_time=$(date +%s.%N)
        if timeout 10 nc -z "$host" "$port" 2>/dev/null; then
            local end_time=$(date +%s.%N)
            local latency=$(echo "scale=1; ($end_time - $start_time) * 1000" | bc -l)
            print_result "TCP connect: ${latency}ms"
        else
            echo "FAILED"
        fi
    done
    
    # Also try HTTP response time test
    echo ""
    echo "HTTP Response Time Tests:"
    local http_targets=("google.com" "cloudflare.com" "github.com")
    
    for target in "${http_targets[@]}"; do
        echo -n "Testing $target HTTP: "
        local start_time=$(date +%s.%N)
        if curl -s --max-time 10 --connect-timeout 5 "http://$target" >/dev/null 2>&1; then
            local end_time=$(date +%s.%N)
            local response_time=$(echo "scale=1; ($end_time - $start_time) * 1000" | bc -l)
            print_result "Response: ${response_time}ms"
        else
            echo "FAILED"
        fi
    done
    echo ""
}

# Test 3: Throughput Performance
test_throughput() {
    print_header "Throughput Tests"
    
    # Test with speedtest-cli if available
    if command -v speedtest-cli >/dev/null 2>&1; then
        echo "Running speedtest-cli..."
        local result=$(speedtest-cli --simple 2>/dev/null)
        if [ $? -eq 0 ]; then
            echo "$result"
        else
            echo "Speedtest failed"
        fi
    else
        echo "speedtest-cli not available"
    fi
    
    # Alternative: Download test
    echo ""
    echo "Download test (10MB file):"
    local start_time=$(date +%s.%N)
    if curl -o /dev/null -s --max-time $TEST_DURATION "http://speedtest.tele2.net/10MB.zip" 2>/dev/null; then
        local end_time=$(date +%s.%N)
        local duration=$(echo "$end_time - $start_time" | bc -l)
        local mbps=$(echo "scale=2; 80 / $duration" | bc -l)  # 10MB = 80 Mbits
        print_result "Download speed: ${mbps} Mbps"
    else
        echo "Download test failed"
    fi
    echo ""
}

# Test 4: VPN Performance Analysis
test_vpn_performance() {
    print_header "VPN Performance Analysis"
    
    # Test current VPN state
    if ip addr show tun0 >/dev/null 2>&1; then
        echo "=== VPN Mode Active ==="
        local vpn_ip=$(curl -s --max-time 10 ifconfig.me)
        
        # Test TCP connection latency since ICMP is blocked
        local start_time=$(date +%s.%N)
        if timeout 5 nc -z ******* 53 2>/dev/null; then
            local end_time=$(date +%s.%N)
            local vpn_latency=$(echo "scale=1; ($end_time - $start_time) * 1000" | bc -l)
        else
            local vpn_latency="FAILED"
        fi
        echo "Public IP: $vpn_ip"
        echo "TCP latency to *******:53: ${vpn_latency}ms"
        
        # Quick download test
        echo "Testing download performance..."
        local start_time=$(date +%s.%N)
        if curl -o /dev/null -s --max-time 15 "http://speedtest.tele2.net/5MB.zip" 2>/dev/null; then
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc -l)
            local vpn_mbps=$(echo "scale=2; 40 / $duration" | bc -l)  # 5MB = 40 Mbits
            echo "Download speed: ${vpn_mbps} Mbps"
        else
            echo "Download test failed"
        fi
        
        # Test multiple connection performance
        echo "Testing concurrent connections..."
        local start_time=$(date +%s.%N)
        for i in {1..3}; do
            timeout 10 nc -z google.com 80 &
            timeout 10 nc -z cloudflare.com 80 &
            timeout 10 nc -z github.com 443 &
        done
        wait
        local end_time=$(date +%s.%N)
        local concurrent_time=$(echo "scale=1; ($end_time - $start_time) * 1000" | bc -l)
        echo "9 concurrent connections: ${concurrent_time}ms"
        
    else
        echo "=== VPN Not Active ==="
        echo "VPN tunnel is not currently active"
        echo "Run 'sudo systemctl start openvpn-client' to start VPN"
    fi
    echo ""
}

# Test 5: Service Status and Health
test_service_health() {
    print_header "VPN Service Health"
    
    echo "=== Service Status ==="
    echo "VPN Service: $(systemctl is-active openvpn-client 2>/dev/null || echo 'not-found')"
    echo "Watchdog Service: $(systemctl is-active openvpn-watchdog 2>/dev/null || echo 'not-found')"
    echo "VPN Tunnel: $(ip addr show tun0 >/dev/null 2>&1 && echo 'UP' || echo 'DOWN')"
    
    echo ""
    echo "=== Connectivity Health ==="
    # Test basic internet connectivity
    if curl -s --max-time 5 ifconfig.me >/dev/null 2>&1; then
        print_result "Internet connectivity: OK"
    else
        echo "Internet connectivity: FAILED"
    fi
    
    # Test DNS resolution
    if nslookup google.com >/dev/null 2>&1; then
        print_result "DNS resolution: OK"
    else
        echo "DNS resolution: FAILED"
    fi
    
    # Test VPN server connectivity
    local vpn_server="{{ openvpn_server_hostname }}"
    local vpn_port="1194"
    if timeout 5 nc -u -z "$vpn_server" "$vpn_port" 2>/dev/null; then
        print_result "VPN server reachable: $vpn_server:$vpn_port"
    else
        echo "VPN server unreachable: $vpn_server:$vpn_port"
    fi
    echo ""
}

# Generate summary report
generate_summary() {
    print_header "Performance Summary"
    
    local report_file="/tmp/vpn-perf-summary-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$report_file" << EOF
VPN Performance Test Summary
============================
Date: $(date)
Server: $(hostname)
VPN Server: {{ openvpn_server_hostname }}

Current Status:
- VPN Service: $(systemctl is-active openvpn-client 2>/dev/null || echo 'not-found')
- Watchdog Service: $(systemctl is-active openvpn-watchdog 2>/dev/null || echo 'not-found')
- VPN Tunnel: $(ip addr show tun0 >/dev/null 2>&1 && echo 'UP' || echo 'DOWN')
- Public IP: $(curl -s --max-time 10 ifconfig.me 2>/dev/null || echo 'Unable to determine')

Key Metrics:
- TCP latency to *******:53: $(timeout 5 bash -c 'start=$(date +%s.%N); nc -z ******* 53 2>/dev/null && end=$(date +%s.%N) && echo "scale=1; ($end - $start) * 1000" | bc -l' || echo 'N/A')ms
- Internet Connectivity: $(curl -s --max-time 5 ifconfig.me >/dev/null 2>&1 && echo 'OK' || echo 'FAILED')

Performance Guidelines:
- TCP Latency < 100ms: Excellent
- TCP Latency 100-200ms: Good
- TCP Latency 200-500ms: Acceptable
- TCP Latency > 500ms: Poor

Note: This is a safe performance test that does not modify VPN configuration.
EOF

    echo "Summary report saved: $report_file"
    cat "$report_file"
}

# Main execution
main() {
    echo "VPN Performance Test (Safe Mode)"
    echo "================================"
    echo ""
    
    # Install bc if not available
    if ! command -v bc >/dev/null 2>&1; then
        echo "Installing bc calculator..."
        sudo apt update && sudo apt install -y bc
    fi
    
    test_current_state
    test_latency
    test_throughput
    test_vpn_performance
    test_service_health
    generate_summary
    
    echo ""
    echo "Performance test completed!"
    echo "This test is safe and does not modify VPN configuration."
}

# Run if executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
