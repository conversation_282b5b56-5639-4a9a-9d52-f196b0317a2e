#!/bin/bash
# OpenVPN Pre-Start Script
# Generated by Ansible - DO NOT EDIT MANUALLY

# Use less strict error handling for systemd compatibility
set -e

# Configuration
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
LOG_FILE="{{ openvpn_client_log_dir }}/pre-start.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting OpenVPN pre-start setup"

# Ensure wired interface is up and configured
log "Ensuring wired interface $WIRED_INTERFACE is up and configured"
ip link set "$WIRED_INTERFACE" up 2>/dev/null || true

# Check if interface has IP address
if ! ip addr show "$WIRED_INTERFACE" | grep -q 'inet '; then
    log "Warning: $WIRED_INTERFACE does not have an IP address"
    # Don't wait or try to get DHCP in systemd context - just log and continue
else
    log "Interface $WIRED_INTERFACE has IP address"
fi

# Get current network configuration
WIRED_IP=$(ip addr show "$WIRED_INTERFACE" | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1 | head -1)
GATEWAY=$(ip route show | grep "^default" | grep "$WIRED_INTERFACE" | awk '{print $3}' | head -1)

log "Network configuration: Interface=$WIRED_INTERFACE, IP=$WIRED_IP, Gateway=$GATEWAY"

# Save original gateway information for backup route
BACKUP_ROUTE_FILE="/var/lib/openvpn/backup-route.conf"
mkdir -p "$(dirname "$BACKUP_ROUTE_FILE")"

if [ -n "$GATEWAY" ] && [ -n "$WIRED_INTERFACE" ]; then
    log "Saving backup route information: Gateway=$GATEWAY, Interface=$WIRED_INTERFACE"
    cat > "$BACKUP_ROUTE_FILE" << EOF
BACKUP_GATEWAY="$GATEWAY"
BACKUP_INTERFACE="$WIRED_INTERFACE"
BACKUP_TIMESTAMP="$(date '+%Y-%m-%d_%H-%M-%S')"
EOF
    chmod 600 "$BACKUP_ROUTE_FILE"
else
    log "Warning: Could not determine gateway/interface for backup route"
fi

# Add route for VPN server to ensure it goes through the wired interface
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
if [ -n "$VPN_SERVER" ] && [ -n "$GATEWAY" ]; then
    VPN_SERVER_IP=$(dig +short "$VPN_SERVER" | head -1)
    if [ -n "$VPN_SERVER_IP" ]; then
        log "Adding route for VPN server $VPN_SERVER ($VPN_SERVER_IP) via $GATEWAY"
        # Remove existing route if it exists, then add new one
        ip route del "$VPN_SERVER_IP/32" 2>/dev/null || true
        ip route add "$VPN_SERVER_IP/32" via "$GATEWAY" dev "$WIRED_INTERFACE" 2>/dev/null || true
    fi
fi

# Ensure TUN module is loaded
log "Loading TUN module"
if ! lsmod | grep -q "^tun "; then
    modprobe tun 2>/dev/null || log "Warning: Could not load TUN module"
else
    log "TUN module already loaded"
fi

# Create TUN device if it doesn't exist
if [ ! -c /dev/net/tun ]; then
    log "Creating TUN device"
    mkdir -p /dev/net 2>/dev/null || true
    mknod /dev/net/tun c 10 200 2>/dev/null || log "Warning: Could not create TUN device"
    chmod 600 /dev/net/tun 2>/dev/null || true
else
    log "TUN device already exists"
fi

# Create script to add backup route after VPN connects
BACKUP_ROUTE_SCRIPT="/var/lib/openvpn/vpn-add-backup-route.sh"
mkdir -p "$(dirname "$BACKUP_ROUTE_SCRIPT")"
cat > "$BACKUP_ROUTE_SCRIPT" << 'EOFSCRIPT'
#!/bin/bash
# Add backup default route with higher metric
# This ensures internet access if VPN fails

BACKUP_ROUTE_FILE="/var/lib/openvpn/backup-route.conf"
LOG_FILE="{{ openvpn_client_log_dir }}/backup-route.log"

log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

if [ -f "$BACKUP_ROUTE_FILE" ]; then
    source "$BACKUP_ROUTE_FILE"

    if [ -n "$BACKUP_GATEWAY" ] && [ -n "$BACKUP_INTERFACE" ]; then
        # Remove any existing backup route
        ip route del default via "$BACKUP_GATEWAY" dev "$BACKUP_INTERFACE" metric 200 2>/dev/null || true

        # Add backup route with high metric (lower priority than VPN)
        if ip route add default via "$BACKUP_GATEWAY" dev "$BACKUP_INTERFACE" metric 200 2>/dev/null; then
            log "Added backup default route via $BACKUP_GATEWAY dev $BACKUP_INTERFACE metric 200"
        else
            log "Warning: Failed to add backup default route"
        fi
    else
        log "Error: Invalid backup route configuration"
    fi
else
    log "Error: Backup route configuration file not found"
fi
EOFSCRIPT

chmod +x "$BACKUP_ROUTE_SCRIPT"
log "Created backup route script: $BACKUP_ROUTE_SCRIPT"

log "Pre-start setup completed successfully"
exit 0
