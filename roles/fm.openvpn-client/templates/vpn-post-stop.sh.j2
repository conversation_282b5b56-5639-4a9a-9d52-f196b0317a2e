#!/bin/bash
# OpenVPN Post-Stop Script
# Generated by Ansible - DO NOT EDIT MANUALLY

# Use less strict error handling for systemd compatibility
set -e

# Configuration
WIRED_INTERFACE="{{ openvpn_client_wired_interface }}"
VPN_CONFIG="{{ openvpn_client_config_dir }}/{{ openvpn_client_config_file }}"
LOG_FILE="{{ openvpn_client_log_dir }}/post-stop.log"

# Logging function
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

log "Starting OpenVPN post-stop cleanup"

# Clean up any remaining OpenVPN processes
log "Cleaning up any remaining OpenVPN processes"
pkill -f "openvpn.*{{ openvpn_client_config_file }}" 2>/dev/null || true

# Remove VPN server route if it exists
VPN_SERVER=$(grep "^remote " "$VPN_CONFIG" | awk '{print $2}' | head -1)
if [ -n "$VPN_SERVER" ]; then
    VPN_SERVER_IP=$(dig +short "$VPN_SERVER" | head -1 2>/dev/null || true)
    if [ -n "$VPN_SERVER_IP" ]; then
        log "Removing route for VPN server $VPN_SERVER ($VPN_SERVER_IP)"
        ip route del "$VPN_SERVER_IP/32" 2>/dev/null || true
    fi
fi

# Restore original default route if backup exists
BACKUP_ROUTE_FILE="/var/lib/openvpn/backup-route.conf"
if [ -f "$BACKUP_ROUTE_FILE" ]; then
    source "$BACKUP_ROUTE_FILE"

    if [ -n "$BACKUP_GATEWAY" ] && [ -n "$BACKUP_INTERFACE" ]; then
        log "Restoring original default route via $BACKUP_GATEWAY dev $BACKUP_INTERFACE"

        # Remove backup route with metric 200
        ip route del default via "$BACKUP_GATEWAY" dev "$BACKUP_INTERFACE" metric 200 2>/dev/null || true

        # Check if there's already a default route
        if ! ip route show default | grep -q "default"; then
            # No default route exists, add the original one back
            if ip route add default via "$BACKUP_GATEWAY" dev "$BACKUP_INTERFACE" 2>/dev/null; then
                log "Restored original default route"
            else
                log "Warning: Failed to restore original default route"
            fi
        else
            log "Default route already exists, not modifying"
        fi
    else
        log "Warning: Invalid backup route configuration"
    fi
else
    log "Warning: No backup route configuration found"
fi

# Network cleanup - just log, don't try to refresh DHCP in systemd context
log "Network cleanup completed for $WIRED_INTERFACE"

log "Post-stop cleanup completed"
