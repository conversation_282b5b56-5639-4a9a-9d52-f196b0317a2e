ArgoCD Role
=========

Install ArgoCD and create Eyecue application

Requirements
------------

<PERSON><PERSON> and Kubernetes must be installed on the target machine


Dependencies
------------

The `kubernetes.core` collection

Example Playbook
----------------

```yaml
- ansible.builtin.import_role:
    name: argocd-helm
    vars:
      argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-qa-helm.git
```

License
-------

Proprietary
