---
# https://argo-cd.readthedocs.io/en/stable/user-guide/sync-kubectl/
- name: Sync the argocd-apps application
  kubernetes.core.k8s_json_patch:
    kind: Application
    api_version: argoproj.io/v1alpha1
    namespace: "{{ argocd_namespace }}"
    name: argocd-apps
    patch:
      - op: add
        path: /operation
        value:
          initiatedBy:
            username: eyecue-server-setup
          sync:
            syncStrategy:
              hook: {}
    kubeconfig: "{{ argocd_kubeconfig_location }}"

- name: Wait until ArgoCD reports sync succeeded
  kubernetes.core.k8s_info:
    kind: Application
    api_version: argoproj.io/v1alpha1
    namespace: "{{ argocd_namespace }}"
    name: argocd-apps
    kubeconfig: "{{ argocd_kubeconfig_location }}"
  register: app_status
  until: app_status.resources[0].status.operationState.phase == "Succeeded"
  retries: 10
  delay: 3

- name: Get all ArgoCD Applications created by the argocd-apps application
  kubernetes.core.k8s_info:
    kind: Application
    api_version: argoproj.io/v1alpha1
    namespace: "{{ argocd_namespace }}"
    kubeconfig: "{{ argocd_kubeconfig_location }}"
  register: argocd_resources

- name: Sync the ArgoCD Applications created by the argocd-apps application
  kubernetes.core.k8s_json_patch:
    kind: Application
    api_version: argoproj.io/v1alpha1
    namespace: "{{ argocd_namespace }}"
    name: "{{ item.metadata.name }}"
    patch:
      - op: add
        path: /operation
        value:
          initiatedBy:
            username: eyecue-server-setup
          sync:
            syncStrategy:
              hook: {}
    kubeconfig: "{{ argocd_kubeconfig_location }}"
  loop: "{{ argocd_resources.resources }}"
  when: item.metadata.name != "argocd-apps"
  loop_control:
    label: "{{ item.metadata.name }}"
