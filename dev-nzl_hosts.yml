all:
  children:
    production:
      children:
        beng_test:
          hosts:
            fm-dev-nzl-00002.eyeq.vpn: {}
          vars:
            address: 31 Napier Road, Havelock North 4130
            country: nzl
            country_code: '036'
            customer: dev
            deployment_env: Production
            display_name: BenG Test
            id: '00002'
            latitude: '-25.7323831126'
            longitude: '134.491117773'
            serial_number: P1001PC05278
            site_contact_email: <EMAIL>
            site_id: fm-dev-nzl-00002
            timezone: Pacific/Auckland
        customer_replica_-_dev_nzl_-_00001:
          hosts:
            fm-dev-nzl-00001.eyeq.vpn: {}
          vars:
            address: 101 Queen Street East, Hastings 4122
            country: nzl
            country_code: '554'
            customer: dev
            deployment_env: Development
            display_name: Customer Replica - Dev NZL - 00001
            id: '00001'
            latitude: '-39.641626'
            longitude: '176.844427'
            serial_number: PC1T2MS5
            site_contact_email: <EMAIL>
            site_id: fm-dev-nzl-00001
            timezone: Pacific/Auckland
        hastings_vst_production:
          hosts:
            fm-dev-nzl-00003.eyeq.vpn: {}
          vars:
            address: 1018 Heretaunga Street West, St Leonards, Hastings 4120
            country: nzl
            country_code: '554'
            customer: dev
            deployment_env: Production
            display_name: Hastings Vst Production
            id: '00003'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: GM0L4Z2Q
            site_contact_email: <EMAIL>
            site_id: fm-dev-nzl-00003
            timezone: Pacific/Auckland
        platform_dev_00002:
          hosts:
            fm-dev-nzl-00002.eyeq.vpn: {}
          vars:
            address: 31 Napier Road, Havelock North 4130
            country: nzl
            country_code: '554'
            customer: dev
            deployment_env: Production
            display_name: Platform Dev 00002
            id: '00002'
            latitude: '-41.8294535363'
            longitude: '171.462581036'
            serial_number: PC2CJXYC
            site_contact_email: <EMAIL>
            site_id: fm-dev-nzl-00002
            timezone: Pacific/Auckland
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-stg-nzl-helm
        aws_s3_bucket_camera_images: eyecue-stg-nzl-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: stg-nzl
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
