all:
  children:
    production:
      children:
        customer_replica_-_stg_nzl_-_00001:
          hosts:
            fm-stg-nzl-00001.eyeq.vpn: {}
          vars:
            address: 101 Queen Street East, Hastings 4122
            country: nzl
            country_code: '554'
            customer: stg
            deployment_env: Staging
            display_name: Customer Replica - Stg NZL - 00001
            id: '00001'
            latitude: '-39.641626'
            longitude: '176.844427'
            serial_number: PC1T2MSA
            site_contact_email: <EMAIL>
            site_id: fm-stg-nzl-00001
            timezone: Pacific/Auckland
        test:
          hosts: fm-dev-nzl-00000.eyeq.vpn
          vars:
            display_name: Test
            timezone: Pacific/Auckland
      vars:
        argocd_bitbucket_repo: *****************:fingermarkltd/eyecue-dev-nzl-helm
        aws_s3_bucket_camera_images: eyecue-dev-nzl-camera-images
        pod_network_cidr: **********/16
  vars:
    ansible_python_interpreter: /usr/bin/python3
    customer: dev-nzl
    deployment_env: Production
    fingermark_product: eyecue
    kubeadmin_config: /etc/kubernetes/admin.conf
