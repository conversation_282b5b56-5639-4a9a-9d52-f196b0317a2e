import subprocess
from datetime import datetime, timedelta, timezone
from typing import Dict, Optional
from ansible.plugins.callback import CallbackBase
from ansible.executor.task_result import TaskResult
from ansible.playbook.task import Task


# Partition key for the table is the site_id (the hostname of the machine)
# Sort key is the timestamp of the task execution
# Global Secondary Index: partition key as the server_serial_number and the sort key as the timestamp
# This hook utilises the SSM Instance STS credentials to write to authenticate with AWS


PROVISIONING_EVENTS_TTL_DAYS = 14


def write_to_dynamodb(
    table, site_id: str, server_serial_number: str, extra_info: Optional[Dict] = None
) -> None:
    """
    Writes task execution details to DynamoDB.
    """
    time_now = datetime.now(timezone.utc)
    timestamp = time_now.isoformat()
    ttl = int((time_now + timedelta(days=PROVISIONING_EVENTS_TTL_DAYS)).timestamp())

    table.put_item(
        Item={
            "site_id": site_id,
            "server_serial_number": server_serial_number,
            "service": "ansible",
            "timestamp": timestamp,
            "extra_info": extra_info or {},
            "ttl": ttl,
        }
    )


def get_serial_number() -> str:
    try:
        result = subprocess.run(
            ["dmidecode", "-s", "system-serial-number"],
            capture_output=True,
            text=True,
            check=True,
        )
        serial = result.stdout.strip()
        if serial and serial.lower() not in ("unknown", "none"):
            return serial
    except subprocess.CalledProcessError:
        pass
    return "unknown"


def get_hostname(result: TaskResult) -> str:
    """
    Get the managed node's hostname. First from the result object, then from /etc/hostname.
    """
    host_name = result._host.get_name()
    if host_name == "localhost":
        try:
            with open("/etc/hostname", "r") as f:
                return f.read().strip()
        except FileNotFoundError:
            return "unknown"
    else:
        return host_name


class CallbackModule(CallbackBase):
    """
    Custom callback plugin to execute hooks at task start and completion.
    """

    CALLBACK_VERSION = 2.0
    CALLBACK_TYPE = "notification"
    CALLBACK_NAME = "task_hooks"

    def __init__(self) -> None:
        super().__init__()
        self.task_start_times: Dict[str, float] = {}
        self.server_serial_number = get_serial_number()
        self.table = None
        self.enabled = False

    def _initialize_aws_resources(self):
        """
        Lazily import boto3 and initialize AWS resources.
        """
        if self.enabled:
            return  # Already initialized

        try:
            import boto3

            session = boto3.Session(region_name="ap-southeast-2")
            self.table = session.resource("dynamodb").Table("platform-provisioning-events")

            sts = session.client("sts")
            sts.get_caller_identity()
            self.enabled = True
        except Exception as e:
            self._display.warning(f"AWS resources could not be initialized: {e}")
            self.enabled = False

    def _log_task_result(
        self, result: TaskResult, status: str, extra_info: Optional[Dict] = None
    ) -> None:
        """
        Logs the task result to DynamoDB if credentials are available.
        """
        if not self.enabled:
            self._initialize_aws_resources()
            if not self.enabled:
                return  # Exit if AWS resources are still unavailable

        hostname = get_hostname(result)
        task_name = result.task_name
        task_id = result._task._uuid
        response = result._result.get("msg", "")

        task_info = {
            "run_id": task_id,
            "task_name": task_name,
            "status": status,
            "response": response,
        }
        if extra_info:
            task_info.update(extra_info)

        write_to_dynamodb(
            self.table,
            site_id=hostname,
            server_serial_number=self.server_serial_number,
            extra_info=task_info,
        )

    def v2_playbook_on_task_start(self, task: Task, is_conditional: bool) -> None:
        """
        Triggered when a task starts.
        """

    def v2_runner_on_ok(self, result: TaskResult) -> None:
        """
        Triggered when a task completes successfully.
        """
        self._log_task_result(result, "success")

    def v2_runner_on_failed(
        self, result: TaskResult, ignore_errors: bool = False
    ) -> None:
        """
        Triggered when a task fails.
        """
        self._log_task_result(result, "failed", result._result)

    def v2_runner_on_skipped(self, result: TaskResult) -> None:
        """
        Triggered when a task is skipped.
        """
        self._log_task_result(result, "skipped")
